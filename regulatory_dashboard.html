<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市网信办 - 数据跨境传输监管平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f6fa;
            min-height: 100vh;
        }
        
        /* 顶部横幅 KPI */
        .header-banner {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .header-icon {
            margin-right: 15px;
            font-size: 32px;
        }
        .kpi-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .kpi-item {
            text-align: center;
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 12px;
            min-width: 180px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .kpi-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .kpi-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }
        
        /* 实时告警滚动条 */
        .alert-ticker {
            background: linear-gradient(90deg, #fef3c7, #fde68a);
            border-left: 4px solid #f59e0b;
            padding: 15px 20px;
            margin: 20px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .ticker-content {
            animation: scroll-left 40s linear infinite;
            white-space: nowrap;
            color: #92400e;
            font-weight: 600;
            font-size: 14px;
        }
        @keyframes scroll-left {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        /* 通用卡片样式 */
        .section-card {
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        .section-header {
            background: linear-gradient(90deg, #f8fafc, #f1f5f9);
            padding: 18px 25px;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 700;
            color: #374151;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }
        .toggle-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: background-color 0.2s;
        }
        .toggle-btn:hover {
            background: #2563eb;
        }
        .collapsible-content {
            padding: 25px;
        }
        
        /* 表格样式 */
        .table-responsive {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
            white-space: nowrap;
        }
        tr:hover {
            background-color: #f9fafb;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-compliant {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-warning {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        /* 地图容器 */
        .map-container {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 2px solid #e5e7eb;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
        }
        
        /* 底部双栏布局 */
        .bottom-section {
            display: flex;
            gap: 20px;
            margin: 20px;
        }
        .radar-container, .files-container {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        /* 雷达图样式 */
        .radar-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f9fafb;
            border-radius: 8px;
        }
        .metric-label {
            font-weight: 600;
            color: #374151;
        }
        .metric-value {
            font-weight: bold;
            font-size: 16px;
        }
        .metric-high { color: #dc2626; }
        .metric-medium { color: #f59e0b; }
        .metric-good { color: #059669; }
        
        /* 文件列表样式 */
        .file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        .file-item:hover {
            background-color: #f0f9ff;
            transform: translateX(5px);
        }
        .file-icon {
            margin-right: 15px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        .file-name {
            font-weight: 600;
            color: #374151;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .kpi-container {
                flex-direction: column;
                align-items: center;
            }
            .bottom-section {
                flex-direction: column;
            }
            .section-card {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部横幅 KPI -->
    <div class="header-banner">
        <div class="header-title">
            <span class="header-icon">🏛️</span>
            市网信办数据跨境传输监管平台
        </div>
        <div class="kpi-container">
            <div class="kpi-item">
                <div class="kpi-value">3</div>
                <div class="kpi-label">辖区单位总数</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">7</div>
                <div class="kpi-label">活跃跨境系统</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">5.8 TB</div>
                <div class="kpi-label">本月出境数据总量</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">2</div>
                <div class="kpi-label">待整改事项</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">94%</div>
                <div class="kpi-label">合规率</div>
            </div>
        </div>
    </div>

    <!-- 实时告警滚动条 -->
    <div class="alert-ticker">
        <div class="ticker-content">
            🚨 2024-08-27 09:45 市公共交通集团 缺少 DPIA 报告，限 7 日内补齐 &nbsp;&nbsp;&nbsp;&nbsp; 
            📋 2024-08-26 16:20 市第一人民医院 新增 0.3 TB 影像备份至新加坡，已自动备案 &nbsp;&nbsp;&nbsp;&nbsp;
            ✅ 2024-08-25 14:30 市教育云公司 完成季度合规审查，状态良好
        </div>
    </div>

    <!-- 单位列表卡片 -->
    <div class="section-card">
        <div class="section-header">
            <span>📊 辖区单位监管档案</span>
            <button class="toggle-btn" onclick="toggleSection('units-table')">展开/收起</button>
        </div>
        <div class="collapsible-content" id="units-table">
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>单位名称</th>
                            <th>行业</th>
                            <th>出境系统</th>
                            <th>本月流量</th>
                            <th>接收国</th>
                            <th>主要类别</th>
                            <th>合规状态</th>
                            <th>最近事件</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>市第一人民医院</strong></td>
                            <td>卫健</td>
                            <td>影像云</td>
                            <td><strong>2.1 TB</strong></td>
                            <td>🇸🇬 新加坡</td>
                            <td>医学影像</td>
                            <td><span class="status-badge status-compliant">合规</span></td>
                            <td>7 天前补传 SCC</td>
                        </tr>
                        <tr>
                            <td><strong>市公共交通集团</strong></td>
                            <td>交通</td>
                            <td>票务大数据</td>
                            <td><strong>1.6 TB</strong></td>
                            <td>🇩🇪 德国</td>
                            <td>乘客出行记录</td>
                            <td><span class="status-badge status-warning">需整改</span></td>
                            <td>缺少 DPIA</td>
                        </tr>
                        <tr>
                            <td><strong>市教育云公司</strong></td>
                            <td>教育</td>
                            <td>在线课堂</td>
                            <td><strong>2.1 TB</strong></td>
                            <td>🇺🇸 美国</td>
                            <td>教学日志</td>
                            <td><span class="status-badge status-compliant">合规</span></td>
                            <td>—</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 数据流向地图 -->
    <div class="section-card">
        <div class="section-header">
            <span>🌍 数据跨境流向监测</span>
        </div>
        <div class="collapsible-content">
            <div class="map-container">
                <!-- 简化中国地图与数据流向 -->
                <svg viewBox="0 0 1000 400" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                    <!-- 渐变和动画定义 -->
                    <defs>
                        <!-- 高风险数据流 -->
                        <linearGradient id="highRiskFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="transparent"/>
                            <stop offset="30%" stop-color="#ef4444" stop-opacity="0.8"/>
                            <stop offset="70%" stop-color="#dc2626" stop-opacity="1"/>
                            <stop offset="100%" stop-color="transparent"/>
                            <animateTransform attributeName="gradientTransform" type="translate"
                                            values="-100 0;100 0;-100 0" dur="2s" repeatCount="indefinite"/>
                        </linearGradient>

                        <!-- 中风险数据流 -->
                        <linearGradient id="mediumRiskFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="transparent"/>
                            <stop offset="30%" stop-color="#f59e0b" stop-opacity="0.8"/>
                            <stop offset="70%" stop-color="#d97706" stop-opacity="1"/>
                            <stop offset="100%" stop-color="transparent"/>
                            <animateTransform attributeName="gradientTransform" type="translate"
                                            values="-100 0;100 0;-100 0" dur="2.5s" repeatCount="indefinite"/>
                        </linearGradient>

                        <!-- 低风险数据流 -->
                        <linearGradient id="lowRiskFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="transparent"/>
                            <stop offset="30%" stop-color="#10b981" stop-opacity="0.8"/>
                            <stop offset="70%" stop-color="#059669" stop-opacity="1"/>
                            <stop offset="100%" stop-color="transparent"/>
                            <animateTransform attributeName="gradientTransform" type="translate"
                                            values="-100 0;100 0;-100 0" dur="3s" repeatCount="indefinite"/>
                        </linearGradient>

                        <!-- 箭头标记 -->
                        <marker id="arrowHigh" markerWidth="10" markerHeight="10" refX="8" refY="3" orient="auto">
                            <path d="M0,0 L0,6 L9,3 z" fill="#ef4444"/>
                        </marker>
                        <marker id="arrowMedium" markerWidth="10" markerHeight="10" refX="8" refY="3" orient="auto">
                            <path d="M0,0 L0,6 L9,3 z" fill="#f59e0b"/>
                        </marker>
                        <marker id="arrowLow" markerWidth="10" markerHeight="10" refX="8" refY="3" orient="auto">
                            <path d="M0,0 L0,6 L9,3 z" fill="#10b981"/>
                        </marker>
                    </defs>

                    <!-- 背景网格 -->
                    <defs>
                        <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                            <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#e5e7eb" stroke-width="0.5" opacity="0.3"/>
                        </pattern>
                    </defs>
                    <rect width="1000" height="400" fill="url(#grid)"/>

                    <!-- 简化的中国轮廓 -->
                    <path d="M 400 120 Q 450 100 520 110 Q 580 120 620 140 Q 650 170 655 200 Q 660 240 640 270 Q 600 290 550 285 Q 500 280 450 270 Q 410 250 405 210 Q 400 170 400 120 Z"
                          fill="#dbeafe" stroke="#2563eb" stroke-width="3" opacity="0.9">
                        <animate attributeName="stroke-width" values="3;5;3" dur="2s" repeatCount="indefinite"/>
                    </path>
                    <text x="530" y="205" font-size="16" font-weight="700" fill="#1e40af" text-anchor="middle">中国</text>

                    <!-- 目标国家/地区 -->
                    <!-- 新加坡 -->
                    <circle cx="750" cy="280" r="25" fill="#dcfce7" stroke="#10b981" stroke-width="2"/>
                    <text x="750" y="285" font-size="12" font-weight="600" fill="#059669" text-anchor="middle">新加坡</text>

                    <!-- 德国 -->
                    <circle cx="200" cy="150" r="25" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
                    <text x="200" y="155" font-size="12" font-weight="600" fill="#d97706" text-anchor="middle">德国</text>

                    <!-- 美国 -->
                    <circle cx="100" cy="250" r="25" fill="#fecaca" stroke="#ef4444" stroke-width="2"/>
                    <text x="100" y="255" font-size="12" font-weight="600" fill="#dc2626" text-anchor="middle">美国</text>

                    <!-- 数据流线 -->
                    <!-- 中国 -> 新加坡 (医院) -->
                    <path d="M 530 205 Q 640 240 750 280" fill="none" stroke="url(#lowRiskFlow)" stroke-width="4" opacity="0.8"/>
                    <path d="M 530 205 Q 640 240 750 280" fill="none" stroke="#10b981" stroke-width="2" opacity="0.6" marker-end="url(#arrowLow)"/>

                    <!-- 中国 -> 德国 (交通) -->
                    <path d="M 530 205 Q 365 175 200 150" fill="none" stroke="url(#mediumRiskFlow)" stroke-width="4" opacity="0.8"/>
                    <path d="M 530 205 Q 365 175 200 150" fill="none" stroke="#f59e0b" stroke-width="2" opacity="0.6" marker-end="url(#arrowMedium)"/>

                    <!-- 中国 -> 美国 (教育) -->
                    <path d="M 530 205 Q 315 225 100 250" fill="none" stroke="url(#highRiskFlow)" stroke-width="4" opacity="0.8"/>
                    <path d="M 530 205 Q 315 225 100 250" fill="none" stroke="#ef4444" stroke-width="2" opacity="0.6" marker-end="url(#arrowHigh)"/>

                    <!-- 数据传输标签 -->
                    <g font-family="Arial, sans-serif" font-size="11" font-weight="600">
                        <text x="640" y="250" fill="#059669" text-anchor="middle">医院 2.1TB</text>
                        <text x="365" y="170" fill="#d97706" text-anchor="middle">交通 1.6TB</text>
                        <text x="315" y="235" fill="#dc2626" text-anchor="middle">教育 2.1TB</text>
                    </g>

                    <!-- 中心数据点 -->
                    <circle cx="530" cy="205" r="8" fill="#2563eb" stroke="#ffffff" stroke-width="2">
                        <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
                    </circle>
                </svg>
            </div>
        </div>
    </div>

    <!-- 底部双栏：风险雷达 + 附件仓库 -->
    <div class="bottom-section">
        <!-- 风险雷达 -->
        <div class="radar-container">
            <div class="section-title">⚡ 风险雷达（辖区维度）</div>

            <!-- 雷达图可视化区域 -->
            <div style="text-align: center; margin: 20px 0;">
                <svg width="200" height="200" viewBox="0 0 200 200">
                    <!-- 雷达图背景 -->
                    <defs>
                        <radialGradient id="radarBg" cx="50%" cy="50%" r="50%">
                            <stop offset="0%" stop-color="#f0f9ff" stop-opacity="0.8"/>
                            <stop offset="100%" stop-color="#e0f2fe" stop-opacity="0.3"/>
                        </radialGradient>
                    </defs>

                    <!-- 同心圆 -->
                    <circle cx="100" cy="100" r="80" fill="none" stroke="#e5e7eb" stroke-width="1"/>
                    <circle cx="100" cy="100" r="60" fill="none" stroke="#e5e7eb" stroke-width="1"/>
                    <circle cx="100" cy="100" r="40" fill="none" stroke="#e5e7eb" stroke-width="1"/>
                    <circle cx="100" cy="100" r="20" fill="none" stroke="#e5e7eb" stroke-width="1"/>

                    <!-- 射线 -->
                    <line x1="100" y1="20" x2="100" y2="180" stroke="#e5e7eb" stroke-width="1"/>
                    <line x1="20" y1="100" x2="180" y2="100" stroke="#e5e7eb" stroke-width="1"/>
                    <line x1="43" y1="43" x2="157" y2="157" stroke="#e5e7eb" stroke-width="1"/>
                    <line x1="157" y1="43" x2="43" y2="157" stroke="#e5e7eb" stroke-width="1"/>

                    <!-- 数据多边形 -->
                    <polygon points="100,44 144,76 156,140 100,124 56,132"
                             fill="rgba(59, 130, 246, 0.3)"
                             stroke="#3b82f6"
                             stroke-width="2"/>

                    <!-- 数据点 -->
                    <circle cx="100" cy="44" r="4" fill="#ef4444"/> <!-- 数据敏感性 70 -->
                    <circle cx="144" cy="76" r="4" fill="#f59e0b"/> <!-- 传输频率 60 -->
                    <circle cx="156" cy="140" r="4" fill="#10b981"/> <!-- 接收国风险 45 -->
                    <circle cx="100" cy="124" r="4" fill="#3b82f6"/> <!-- 合同完整度 80 -->
                    <circle cx="56" cy="132" r="4" fill="#059669"/> <!-- 加密强度 90 -->
                </svg>
            </div>

            <div class="radar-metrics">
                <div class="metric-item">
                    <span class="metric-label">数据敏感性</span>
                    <span class="metric-value metric-high">70</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">传输频率</span>
                    <span class="metric-value metric-medium">60</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">接收国风险</span>
                    <span class="metric-value metric-good">45</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">合同完整度</span>
                    <span class="metric-value metric-good">80</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">加密强度</span>
                    <span class="metric-value metric-good">90</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">综合评分</span>
                    <span class="metric-value metric-medium">69</span>
                </div>
            </div>
        </div>

        <!-- 附件仓库 -->
        <div class="files-container">
            <div class="section-title">📁 附件仓库（监管侧归档）</div>

            <div class="file-item">
                <span class="file-icon">📄</span>
                <div>
                    <div class="file-name">市第一人民医院-SCC-2024-SG.pdf</div>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                        标准合同条款 • 2.3MB • 2024-08-20
                    </div>
                </div>
            </div>

            <div class="file-item">
                <span class="file-icon">📊</span>
                <div>
                    <div class="file-name">市公共交通集团-DPIA-Template.xlsx</div>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                        数据保护影响评估模板 • 1.8MB • 2024-08-15
                    </div>
                </div>
            </div>

            <div class="file-item">
                <span class="file-icon">🔒</span>
                <div>
                    <div class="file-name">市教育云公司-加密测评报告.pdf</div>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                        加密技术评估报告 • 4.1MB • 2024-08-10
                    </div>
                </div>
            </div>

            <div class="file-item">
                <span class="file-icon">📋</span>
                <div>
                    <div class="file-name">辖区数据跨境月度汇总-2024-08.pdf</div>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                        监管月报 • 6.7MB • 2024-08-25
                    </div>
                </div>
            </div>

            <div class="file-item">
                <span class="file-icon">⚖️</span>
                <div>
                    <div class="file-name">数据跨境安全评估指南-v2.1.pdf</div>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                        监管指导文件 • 3.2MB • 2024-07-30
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 1px solid #e5e7eb;">
                <button style="background: #f3f4f6; border: 1px solid #d1d5db; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px; color: #6b7280;">
                    查看更多归档文件 →
                </button>
            </div>
        </div>
    </div>

    <script>
        // 切换区块展开/收起功能
        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section.style.display === 'none') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }

        // 文件点击事件
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', function() {
                const fileName = this.querySelector('.file-name').textContent;
                alert('正在下载文件：' + fileName);
            });
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('市网信办数据跨境传输监管平台已加载');
        });
    </script>
</body>
</html>
