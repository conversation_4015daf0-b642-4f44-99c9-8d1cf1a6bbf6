<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市网信办 - 数据跨境传输监管平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            display: flex;
            background-color: #f5f6fa;
            min-height: 100vh;
        }

        /* 左侧菜单栏 */
        .sidebar {
            width: 250px;
            background-color: #001529;
            height: 100vh;
            position: sticky;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s ease;
            color: #fff;
        }
        .logo {
            padding: 20px;
            border-bottom: 1px solid #1a2d3d;
            text-align: center;
            font-size: 16px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .logo-icon {
            margin-bottom: 8px;
            font-size: 28px;
        }
        .menu {
            list-style: none;
        }
        .menu-item {
            padding: 16px 20px;
            border-bottom: 1px solid #1a2d3d;
            cursor: pointer;
            font-size: 14px;
            color: #a6adb4;
            position: relative;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }
        .menu-item.active {
            background-color: #1890ff;
            color: #fff;
        }
        .menu-item:hover {
            background-color: #1a2d3d;
            color: #fff;
        }
        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        /* 右侧内容区域 */
        .content {
            flex: 1;
            overflow-x: auto;
        }

        /* 顶部横幅 KPI */
        .header-banner {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .kpi-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .kpi-item {
            text-align: center;
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 8px;
            min-width: 140px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .kpi-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 6px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .kpi-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }
        
        /* 实时告警滚动条 */
        .alert-ticker {
            background: linear-gradient(90deg, #fef3c7, #fde68a);
            border-left: 4px solid #f59e0b;
            padding: 15px 20px;
            margin: 20px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .ticker-content {
            animation: scroll-left 40s linear infinite;
            white-space: nowrap;
            color: #92400e;
            font-weight: 600;
            font-size: 14px;
        }
        @keyframes scroll-left {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        /* 通用卡片样式 */
        .section-card {
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        .section-header {
            background: linear-gradient(90deg, #f8fafc, #f1f5f9);
            padding: 18px 25px;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 700;
            color: #374151;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }
        .toggle-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: background-color 0.2s;
        }
        .toggle-btn:hover {
            background: #2563eb;
        }
        .collapsible-content {
            padding: 25px;
        }
        
        /* 表格样式 */
        .table-responsive {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
            white-space: nowrap;
        }
        tr:hover {
            background-color: #f9fafb;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-compliant {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-warning {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        /* 地图容器 */
        .map-container {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 2px solid #e5e7eb;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
        }
        
        /* 底部双栏布局 */
        .bottom-section {
            display: flex;
            gap: 20px;
            margin: 20px;
        }
        .radar-container, .files-container {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        /* 雷达图样式 */
        .radar-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f9fafb;
            border-radius: 8px;
        }
        .metric-label {
            font-weight: 600;
            color: #374151;
        }
        .metric-value {
            font-weight: bold;
            font-size: 16px;
        }
        .metric-high { color: #dc2626; }
        .metric-medium { color: #f59e0b; }
        .metric-good { color: #059669; }
        
        /* 文件列表样式 */
        .file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        .file-item:hover {
            background-color: #f0f9ff;
            transform: translateX(5px);
        }
        .file-icon {
            margin-right: 15px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        .file-name {
            font-weight: 600;
            color: #374151;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .kpi-container {
                flex-direction: column;
                align-items: center;
            }
            .bottom-section {
                flex-direction: column;
            }
            .section-card {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
        <div class="logo">
            <div class="logo-icon">🏛️</div>
            <div>市网信办</div>
            <div style="font-size: 12px; opacity: 0.8; margin-top: 4px;">监管平台</div>
        </div>
        <ul class="menu">
            <li class="menu-item active">
                <i>📊</i>
                <span>数据跨境监管</span>
            </li>
            <li class="menu-item">
                <i>🔍</i>
                <span>合规审查</span>
            </li>
            <li class="menu-item">
                <i>⚠️</i>
                <span>风险预警</span>
            </li>
            <li class="menu-item">
                <i>📋</i>
                <span>档案管理</span>
            </li>
            <li class="menu-item">
                <i>📈</i>
                <span>统计分析</span>
            </li>
            <li class="menu-item">
                <i>⚙️</i>
                <span>系统设置</span>
            </li>
        </ul>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content">
        <!-- 顶部横幅 KPI -->
        <div class="header-banner">
        <div class="header-title">
            数据跨境传输监管总览
        </div>
        <div class="kpi-container">
            <div class="kpi-item">
                <div class="kpi-value">3</div>
                <div class="kpi-label">辖区单位总数</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">7</div>
                <div class="kpi-label">活跃跨境系统</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">5.8 TB</div>
                <div class="kpi-label">本月出境数据总量</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">2</div>
                <div class="kpi-label">待整改事项</div>
            </div>
            <div class="kpi-item">
                <div class="kpi-value">94%</div>
                <div class="kpi-label">合规率</div>
            </div>
        </div>
    </div>

    <!-- 实时告警滚动条 -->
    <div class="alert-ticker">
        <div class="ticker-content">
            🚨 2024-08-27 09:45 市公共交通集团 缺少 DPIA 报告，限 7 日内补齐 &nbsp;&nbsp;&nbsp;&nbsp; 
            📋 2024-08-26 16:20 市第一人民医院 新增 0.3 TB 影像备份至新加坡，已自动备案 &nbsp;&nbsp;&nbsp;&nbsp;
            ✅ 2024-08-25 14:30 市教育云公司 完成季度合规审查，状态良好
        </div>
    </div>

    <!-- 单位列表卡片 -->
    <div class="section-card">
        <div class="section-header">
            <span>📊 辖区单位监管档案</span>
            <button class="toggle-btn" onclick="toggleSection('units-table')">展开/收起</button>
        </div>
        <div class="collapsible-content" id="units-table">
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>单位名称</th>
                            <th>行业</th>
                            <th>出境系统</th>
                            <th>本月流量</th>
                            <th>接收国</th>
                            <th>主要类别</th>
                            <th>合规状态</th>
                            <th>最近事件</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>市第一人民医院</strong></td>
                            <td>卫健</td>
                            <td>影像云</td>
                            <td><strong>2.1 TB</strong></td>
                            <td>🇸🇬 新加坡</td>
                            <td>医学影像</td>
                            <td><span class="status-badge status-compliant">合规</span></td>
                            <td>7 天前补传 SCC</td>
                        </tr>
                        <tr>
                            <td><strong>市公共交通集团</strong></td>
                            <td>交通</td>
                            <td>票务大数据</td>
                            <td><strong>1.6 TB</strong></td>
                            <td>🇩🇪 德国</td>
                            <td>乘客出行记录</td>
                            <td><span class="status-badge status-warning">需整改</span></td>
                            <td>缺少 DPIA</td>
                        </tr>
                        <tr>
                            <td><strong>市教育云公司</strong></td>
                            <td>教育</td>
                            <td>在线课堂</td>
                            <td><strong>2.1 TB</strong></td>
                            <td>🇺🇸 美国</td>
                            <td>教学日志</td>
                            <td><span class="status-badge status-compliant">合规</span></td>
                            <td>—</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 数据流向地图 -->
    <div class="section-card">
        <div class="section-header">
            <span>🌍 数据跨境流向监测</span>
        </div>
        <div class="collapsible-content">
            <div class="map-container">
                <!-- 简化中国地图与数据流向 -->
                <svg viewBox="0 0 1000 400" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                    <!-- 渐变和动画定义 -->
                    <defs>
                        <!-- 高风险数据流 -->
                        <linearGradient id="highRiskFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="transparent"/>
                            <stop offset="30%" stop-color="#ef4444" stop-opacity="0.8"/>
                            <stop offset="70%" stop-color="#dc2626" stop-opacity="1"/>
                            <stop offset="100%" stop-color="transparent"/>
                            <animateTransform attributeName="gradientTransform" type="translate"
                                            values="-100 0;100 0;-100 0" dur="2s" repeatCount="indefinite"/>
                        </linearGradient>

                        <!-- 中风险数据流 -->
                        <linearGradient id="mediumRiskFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="transparent"/>
                            <stop offset="30%" stop-color="#f59e0b" stop-opacity="0.8"/>
                            <stop offset="70%" stop-color="#d97706" stop-opacity="1"/>
                            <stop offset="100%" stop-color="transparent"/>
                            <animateTransform attributeName="gradientTransform" type="translate"
                                            values="-100 0;100 0;-100 0" dur="2.5s" repeatCount="indefinite"/>
                        </linearGradient>

                        <!-- 低风险数据流 -->
                        <linearGradient id="lowRiskFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="transparent"/>
                            <stop offset="30%" stop-color="#10b981" stop-opacity="0.8"/>
                            <stop offset="70%" stop-color="#059669" stop-opacity="1"/>
                            <stop offset="100%" stop-color="transparent"/>
                            <animateTransform attributeName="gradientTransform" type="translate"
                                            values="-100 0;100 0;-100 0" dur="3s" repeatCount="indefinite"/>
                        </linearGradient>

                        <!-- 箭头标记 -->
                        <marker id="arrowHigh" markerWidth="10" markerHeight="10" refX="8" refY="3" orient="auto">
                            <path d="M0,0 L0,6 L9,3 z" fill="#ef4444"/>
                        </marker>
                        <marker id="arrowMedium" markerWidth="10" markerHeight="10" refX="8" refY="3" orient="auto">
                            <path d="M0,0 L0,6 L9,3 z" fill="#f59e0b"/>
                        </marker>
                        <marker id="arrowLow" markerWidth="10" markerHeight="10" refX="8" refY="3" orient="auto">
                            <path d="M0,0 L0,6 L9,3 z" fill="#10b981"/>
                        </marker>
                    </defs>

                    <!-- 背景网格 -->
                    <defs>
                        <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                            <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#e5e7eb" stroke-width="0.5" opacity="0.3"/>
                        </pattern>
                    </defs>
                    <rect width="1000" height="400" fill="url(#grid)"/>

                    <!-- 简化的中国轮廓 -->
                    <path d="M 400 120 Q 450 100 520 110 Q 580 120 620 140 Q 650 170 655 200 Q 660 240 640 270 Q 600 290 550 285 Q 500 280 450 270 Q 410 250 405 210 Q 400 170 400 120 Z"
                          fill="#dbeafe" stroke="#2563eb" stroke-width="3" opacity="0.9">
                        <animate attributeName="stroke-width" values="3;5;3" dur="2s" repeatCount="indefinite"/>
                    </path>
                    <text x="530" y="205" font-size="16" font-weight="700" fill="#1e40af" text-anchor="middle">中国</text>

                    <!-- 目标国家/地区 -->
                    <!-- 新加坡 -->
                    <circle cx="750" cy="280" r="25" fill="#dcfce7" stroke="#10b981" stroke-width="2"/>
                    <text x="750" y="285" font-size="12" font-weight="600" fill="#059669" text-anchor="middle">新加坡</text>

                    <!-- 德国 -->
                    <circle cx="200" cy="150" r="25" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
                    <text x="200" y="155" font-size="12" font-weight="600" fill="#d97706" text-anchor="middle">德国</text>

                    <!-- 美国 -->
                    <circle cx="100" cy="250" r="25" fill="#fecaca" stroke="#ef4444" stroke-width="2"/>
                    <text x="100" y="255" font-size="12" font-weight="600" fill="#dc2626" text-anchor="middle">美国</text>

                    <!-- 数据流线 -->
                    <!-- 中国 -> 新加坡 (医院) -->
                    <path d="M 530 205 Q 640 240 750 280" fill="none" stroke="url(#lowRiskFlow)" stroke-width="4" opacity="0.8"/>
                    <path d="M 530 205 Q 640 240 750 280" fill="none" stroke="#10b981" stroke-width="2" opacity="0.6" marker-end="url(#arrowLow)"/>

                    <!-- 中国 -> 德国 (交通) -->
                    <path d="M 530 205 Q 365 175 200 150" fill="none" stroke="url(#mediumRiskFlow)" stroke-width="4" opacity="0.8"/>
                    <path d="M 530 205 Q 365 175 200 150" fill="none" stroke="#f59e0b" stroke-width="2" opacity="0.6" marker-end="url(#arrowMedium)"/>

                    <!-- 中国 -> 美国 (教育) -->
                    <path d="M 530 205 Q 315 225 100 250" fill="none" stroke="url(#highRiskFlow)" stroke-width="4" opacity="0.8"/>
                    <path d="M 530 205 Q 315 225 100 250" fill="none" stroke="#ef4444" stroke-width="2" opacity="0.6" marker-end="url(#arrowHigh)"/>

                    <!-- 数据传输标签 -->
                    <g font-family="Arial, sans-serif" font-size="11" font-weight="600">
                        <text x="640" y="250" fill="#059669" text-anchor="middle">医院 2.1TB</text>
                        <text x="365" y="170" fill="#d97706" text-anchor="middle">交通 1.6TB</text>
                        <text x="315" y="235" fill="#dc2626" text-anchor="middle">教育 2.1TB</text>
                    </g>

                    <!-- 中心数据点 -->
                    <circle cx="530" cy="205" r="8" fill="#2563eb" stroke="#ffffff" stroke-width="2">
                        <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
                    </circle>
                </svg>
            </div>
        </div>
    </div>

    <!-- 底部双栏：档案趋势 + 数据类型分布 -->
    <div class="bottom-section">
        <!-- 跨境档案创建趋势 -->
        <div class="radar-container">
            <div class="section-title">📈 跨境档案创建趋势（近30天）</div>

            <div style="padding: 20px;">
                <svg width="100%" height="280" viewBox="0 0 500 280">
                    <!-- 坐标轴 -->
                    <line x1="50" y1="230" x2="450" y2="230" stroke="#e5e7eb" stroke-width="2" />
                    <line x1="50" y1="50" x2="50" y2="230" stroke="#e5e7eb" stroke-width="2" />

                    <!-- 网格线 -->
                    <g stroke="#f3f4f6" stroke-width="1" opacity="0.5">
                        <line x1="50" y1="200" x2="450" y2="200"/>
                        <line x1="50" y1="170" x2="450" y2="170"/>
                        <line x1="50" y1="140" x2="450" y2="140"/>
                        <line x1="50" y1="110" x2="450" y2="110"/>
                        <line x1="50" y1="80" x2="450" y2="80"/>
                    </g>

                    <!-- 折线图 -->
                    <polyline points="50,210 90,200 130,205 170,190 210,180 250,185 290,160 330,170 370,150 410,140 450,135"
                            fill="none" stroke="#3b82f6" stroke-width="3" />

                    <!-- 填充区域 -->
                    <polygon points="50,210 90,200 130,205 170,190 210,180 250,185 290,160 330,170 370,150 410,140 450,135 450,230 50,230"
                             fill="url(#trendGradient)" opacity="0.3"/>

                    <!-- 渐变定义 -->
                    <defs>
                        <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stop-color="#3b82f6" stop-opacity="0.8"/>
                            <stop offset="100%" stop-color="#3b82f6" stop-opacity="0.1"/>
                        </linearGradient>
                    </defs>

                    <!-- 数据点 -->
                    <circle cx="50" cy="210" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="90" cy="200" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="130" cy="205" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="170" cy="190" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="210" cy="180" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="250" cy="185" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="290" cy="160" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="330" cy="170" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="370" cy="150" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="410" cy="140" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                    <circle cx="450" cy="135" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>

                    <!-- X轴标签 -->
                    <g font-family="Arial, sans-serif" font-size="11" fill="#6b7280" text-anchor="middle">
                        <text x="50" y="250">8/1</text>
                        <text x="130" y="250">8/8</text>
                        <text x="210" y="250">8/15</text>
                        <text x="290" y="250">8/22</text>
                        <text x="370" y="250">8/29</text>
                        <text x="450" y="250">今日</text>
                    </g>

                    <!-- Y轴标签 -->
                    <g font-family="Arial, sans-serif" font-size="11" fill="#6b7280" text-anchor="end">
                        <text x="45" y="235">0</text>
                        <text x="45" y="205">5</text>
                        <text x="45" y="175">10</text>
                        <text x="45" y="145">15</text>
                        <text x="45" y="115">20</text>
                        <text x="45" y="85">25</text>
                        <text x="45" y="55">30</text>
                    </g>

                    <!-- 标题 -->
                    <text x="250" y="30" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#374151" text-anchor="middle">档案数量</text>
                </svg>
            </div>
        </div>

        <!-- 跨境数据类型分布 -->
        <div class="files-container">
            <div class="section-title">📊 跨境数据类型分布</div>

            <div style="padding: 20px;">
                <svg width="100%" height="280" viewBox="0 0 400 280">
                    <!-- 饼图 -->
                    <g transform="translate(200,140)">
                        <!-- 医学影像 (35%) -->
                        <path d="M 0 -80 A 80 80 0 0 1 55.4 57.6 L 0 0 Z" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>

                        <!-- 乘客出行记录 (28%) -->
                        <path d="M 55.4 57.6 A 80 80 0 0 1 -25.9 75.3 L 0 0 Z" fill="#10b981" stroke="#ffffff" stroke-width="2"/>

                        <!-- 教学日志 (25%) -->
                        <path d="M -25.9 75.3 A 80 80 0 0 1 -76.4 -20.9 L 0 0 Z" fill="#f59e0b" stroke="#ffffff" stroke-width="2"/>

                        <!-- 其他 (12%) -->
                        <path d="M -76.4 -20.9 A 80 80 0 0 1 0 -80 L 0 0 Z" fill="#ef4444" stroke="#ffffff" stroke-width="2"/>

                        <!-- 中心圆 -->
                        <circle cx="0" cy="0" r="35" fill="#ffffff" stroke="#e5e7eb" stroke-width="2"/>
                        <text x="0" y="-5" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="#374151" text-anchor="middle">总计</text>
                        <text x="0" y="10" font-family="Arial, sans-serif" font-size="14" font-weight="700" fill="#1f2937" text-anchor="middle">5.8TB</text>
                    </g>

                    <!-- 图例 -->
                    <g font-family="Arial, sans-serif" font-size="12" fill="#374151">
                        <rect x="50" y="50" width="12" height="12" fill="#3b82f6"/>
                        <text x="70" y="61">医学影像 (35%)</text>

                        <rect x="50" y="75" width="12" height="12" fill="#10b981"/>
                        <text x="70" y="86">乘客出行记录 (28%)</text>

                        <rect x="50" y="100" width="12" height="12" fill="#f59e0b"/>
                        <text x="70" y="111">教学日志 (25%)</text>

                        <rect x="50" y="125" width="12" height="12" fill="#ef4444"/>
                        <text x="70" y="136">其他 (12%)</text>
                    </g>

                    <!-- 数据详情 -->
                    <g font-family="Arial, sans-serif" font-size="11" fill="#6b7280">
                        <text x="70" y="175">• 市第一人民医院: 2.1TB</text>
                        <text x="70" y="195">• 市公共交通集团: 1.6TB</text>
                        <text x="70" y="215">• 市教育云公司: 2.1TB</text>
                        <text x="70" y="235">• 其他单位合计: 0.7TB</text>
                    </g>
                </svg>
            </div>
        </div>
    </div>

    <script>
        // 切换区块展开/收起功能
        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section.style.display === 'none') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }

        // 文件点击事件
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', function() {
                const fileName = this.querySelector('.file-name').textContent;
                alert('正在下载文件：' + fileName);
            });
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('市网信办数据跨境传输监管平台已加载');
        });
    </script>
    </div> <!-- 关闭 content div -->
</body>
</html>
