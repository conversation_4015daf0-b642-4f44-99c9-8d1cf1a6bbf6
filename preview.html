<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据安全研判分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            display: flex;
            background-color: #f5f6fa;
            min-height: 100vh;
        }
        .sidebar {
            width: 220px;
            background-color: #001529;
            height: 100vh;
            position: sticky;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s ease;
            color: #fff;
        }
        .logo {
            padding: 20px;
            border-bottom: 1px solid #1a2d3d;
            text-align: center;
            font-size: 16px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .menu {
            list-style: none;
        }
        .menu-item {
            padding: 14px 20px;
            border-bottom: 1px solid #1a2d3d;
            cursor: pointer;
            font-size: 14px;
            color: #a6adb4;
            position: relative;
            transition: all 0.2s ease;
        }
        .menu-item.active {
            background-color: #1890ff;
            color: #fff;
        }
        .menu-item:hover {
            background-color: #1a2d3d;
            color: #fff;
        }
        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .submenu {
            list-style: none;
            padding-left: 20px;
            display: none;
            background-color: #000c17;
        }
        .menu-item.active .submenu {
            display: block;
        }
        .submenu-item {
            padding: 12px 15px;
            font-size: 13px;
            color: #a6adb4;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .submenu-item.active {
            color: #1890ff;
            font-weight: 500;
        }
        .submenu-item:hover {
            color: #fff;
        }
        .content {
            flex: 1;
            padding: 20px;
            overflow-x: auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .title {
            font-size: 24px;
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            flex: 1;
            min-width: 220px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-value.green {
            color: #52c41a;
        }
        .stat-value.blue {
            color: #1890ff;
        }
        .stat-value.orange {
            color: #faad14;
        }
        .stat-value.red {
            color: #f5222d;
        }
        .search-bar {
            display: flex;
            margin-bottom: 20px;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        .search-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        .search-btn {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }
        .search-btn:hover {
            background-color: #40a9ff;
        }
        .table-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #fafafa;
            font-weight: 500;
            color: #333;
            white-space: nowrap;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status {
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.green {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.orange {
            background-color: #fff7e6;
            color: #faad14;
            border: 1px solid #ffd591;
        }
        .status.red {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }
        .chart-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .chart {
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 300px;
            min-height: 300px;
        }
        .chart-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.2s ease;
        }
        .tab.active {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
        }
        .tab:hover {
            color: #1890ff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .alert-level {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .alert-level.high {
            background-color: #f5222d;
        }
        .alert-level.medium {
            background-color: #faad14;
        }
        .alert-level.low {
            background-color: #52c41a;
        }
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .page-item {
            margin: 0 5px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .page-item.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .page-item:hover:not(.active) {
            border-color: #1890ff;
            color: #1890ff;
        }
        /* 图表模拟样式 */
        .chart-placeholder {
            width: 100%;
            height: 250px;
            background-color: #f9f9f9;
            border: 1px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
        /* 地图容器 */
        .map-container {
            width: 100%;
            height: 400px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        .map-overlay {
            /* SVG地图直接嵌入，不需要CSS背景 */
        }
        /* 数据点样式 */
        .data-point {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #f5222d;
            transform: translate(-50%, -50%);
            cursor: pointer;
            box-shadow: 0 0 0 2px rgba(255,255,255,0.8);
        }
        .data-point.medium {
            background-color: #faad14;
        }
        .data-point.low {
            background-color: #52c41a;
        }
        /* 现代化数据流向线 */
        .modern-data-flow {
            position: absolute;
            border-radius: 2px;
            transform-origin: left center;
            opacity: 0.8;
            filter: blur(0.3px);
        }
        
        .high-risk-flow {
            animation: highRiskFlow 3s ease-in-out infinite;
        }
        
        .medium-risk-flow {
            animation: mediumRiskFlow 4s ease-in-out infinite;
        }
        
        .low-risk-flow {
            animation: lowRiskFlow 5s ease-in-out infinite;
        }
        
        @keyframes highRiskFlow {
            0% { opacity: 0.4; transform: translateX(-8px) scale(0.9); }
            50% { opacity: 1; transform: translateX(0) scale(1.1); }
            100% { opacity: 0.4; transform: translateX(8px) scale(0.9); }
        }
        
        @keyframes mediumRiskFlow {
            0% { opacity: 0.3; transform: translateX(-5px) scale(0.95); }
            50% { opacity: 0.9; transform: translateX(0) scale(1.05); }
            100% { opacity: 0.3; transform: translateX(5px) scale(0.95); }
        }
        
        @keyframes lowRiskFlow {
            0% { opacity: 0.2; transform: translateX(-3px) scale(0.98); }
            50% { opacity: 0.7; transform: translateX(0) scale(1.02); }
            100% { opacity: 0.2; transform: translateX(3px) scale(0.98); }
        }
        
        /* 现代化数据点样式 */
        .data-point {
            position: absolute;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;
            border: 2px solid rgba(255, 255, 255, 0.9);
        }
        
        .china-center {
            animation: chinaGlow 2s ease-in-out infinite;
        }
        
        @keyframes chinaGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 40px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 60px rgba(59, 130, 246, 0.5); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.2); }
        }
        
        .data-point:hover {
            transform: translate(-50%, -50%) scale(1.4);
            z-index: 20;
        }
        
        .high-risk:hover {
            box-shadow: 0 0 25px rgba(239, 68, 68, 0.8), 0 0 50px rgba(239, 68, 68, 0.4) !important;
        }
        
        .medium-risk:hover {
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 40px rgba(245, 158, 11, 0.4) !important;
        }
        
        .low-risk:hover {
            box-shadow: 0 0 15px rgba(16, 185, 129, 0.8), 0 0 30px rgba(16, 185, 129, 0.4) !important;
        }
        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }
            .logo span, .menu-item span {
                display: none;
            }
            .content {
                padding: 10px;
            }
            .stat-card {
                min-width: 100%;
            }
            .chart {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <i class="logo-icon">🔒</i>
            <span>数据安全中心</span>
        </div>
        <ul class="menu">
            <li class="menu-item">
                <i>📊</i>
                <span>总览</span>
            </li>
            <li class="menu-item active">
                <i>🔍</i>
                <span>数据安全研判分析</span>
                <ul class="submenu">
                    <li class="submenu-item active">暗网数据监测</li>
                    <li class="submenu-item">互联网暴露面监测</li>
                    <li class="submenu-item">异常跨境数据研判分析</li>
                </ul>
            </li>
            <li class="menu-item">
                <i>🛡️</i>
                <span>安全防护</span>
            </li>
            <li class="menu-item">
                <i>⚠️</i>
                <span>威胁情报</span>
            </li>
            <li class="menu-item">
                <i>📝</i>
                <span>合规管理</span>
            </li>
            <li class="menu-item">
                <i>⚙️</i>
                <span>系统设置</span>
            </li>
        </ul>
    </div>

    <div class="content">
        <div class="header">
            <div class="title">数据安全研判分析 - 暗网数据监测</div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-title">今日暗网敏感信息</div>
                <div class="stat-value red">12</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">本周暗网敏感信息</div>
                <div class="stat-value orange">67</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">高危信息占比</div>
                <div class="stat-value blue">60.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">已处置信息</div>
                <div class="stat-value green">43</div>
            </div>
        </div>

        <div class="tabs">
            <div class="tab active">暗网数据监测</div>
            <div class="tab">互联网暴露面监测</div>
            <div class="tab">异常跨境数据研判分析</div>
        </div>

        <div class="tab-content active" id="darknet-monitoring">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="输入关键词、单位名称或IP地址搜索">
                <button class="search-btn">搜索</button>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-title">暗网敏感信息类型分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 饼图模拟 -->
                            <circle cx="150" cy="125" r="80" fill="#f5f5f5" stroke="#ddd" />
                            <path d="M 150 125 L 150 45 A 80 80 0 0 1 223 162 Z" fill="#f5222d" />
                            <path d="M 150 125 L 223 162 A 80 80 0 0 1 150 205 Z" fill="#faad14" />
                            <path d="M 150 125 L 150 205 A 80 80 0 0 1 77 162 Z" fill="#1890ff" />
                            <path d="M 150 125 L 77 162 A 80 80 0 0 1 150 45 Z" fill="#52c41a" />
                            
                            <!-- 图例 -->
                            <rect x="260" y="80" width="15" height="15" fill="#f5222d" />
                            <text x="285" y="93" font-size="12" fill="#666">数据泄露 (45%)</text>

                            <rect x="260" y="110" width="15" height="15" fill="#faad14" />
                            <text x="285" y="123" font-size="12" fill="#666">账号交易 (28%)</text>

                            <rect x="260" y="140" width="15" height="15" fill="#1890ff" />
                            <text x="285" y="153" font-size="12" fill="#666">黑客攻击 (18%)</text>

                            <rect x="260" y="170" width="15" height="15" fill="#52c41a" />
                            <text x="285" y="183" font-size="12" fill="#666">其他 (9%)</text>
                        </svg>
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-title">暗网敏感信息趋势（近30天）</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 500 250">
                            <!-- 坐标轴 -->
                            <line x1="50" y1="200" x2="450" y2="200" stroke="#ddd" stroke-width="1" />
                            <line x1="50" y1="50" x2="50" y2="200" stroke="#ddd" stroke-width="1" />
                            
                            <!-- 折线图 -->
                            <polyline points="50,180 80,170 110,175 140,160 170,150 200,155 230,130 260,140 290,120 320,100 350,110 380,90 410,70 440,80" 
                                    fill="none" stroke="#f5222d" stroke-width="2" />
                            
                            <!-- 数据点 -->
                            <circle cx="50" cy="180" r="3" fill="#f5222d" />
                            <circle cx="80" cy="170" r="3" fill="#f5222d" />
                            <circle cx="110" cy="175" r="3" fill="#f5222d" />
                            <circle cx="140" cy="160" r="3" fill="#f5222d" />
                            <circle cx="170" cy="150" r="3" fill="#f5222d" />
                            <circle cx="200" cy="155" r="3" fill="#f5222d" />
                            <circle cx="230" cy="130" r="3" fill="#f5222d" />
                            <circle cx="260" cy="140" r="3" fill="#f5222d" />
                            <circle cx="290" cy="120" r="3" fill="#f5222d" />
                            <circle cx="320" cy="100" r="3" fill="#f5222d" />
                            <circle cx="350" cy="110" r="3" fill="#f5222d" />
                            <circle cx="380" cy="90" r="3" fill="#f5222d" />
                            <circle cx="410" cy="70" r="3" fill="#f5222d" />
                            <circle cx="440" cy="80" r="3" fill="#f5222d" />
                            
                            <!-- 坐标轴标签 -->
                            <text x="45" y="220" font-size="10" text-anchor="end" fill="#999">1日</text>
                            <text x="140" y="220" font-size="10" text-anchor="middle" fill="#999">10日</text>
                            <text x="230" y="220" font-size="10" text-anchor="middle" fill="#999">15日</text>
                            <text x="320" y="220" font-size="10" text-anchor="middle" fill="#999">20日</text>
                            <text x="410" y="220" font-size="10" text-anchor="middle" fill="#999">25日</text>
                            
                            <text x="40" y="200" font-size="10" text-anchor="end" fill="#999">0</text>
                            <text x="40" y="150" font-size="10" text-anchor="end" fill="#999">10</text>
                            <text x="40" y="100" font-size="10" text-anchor="end" fill="#999">20</text>
                            <text x="40" y="50" font-size="10" text-anchor="end" fill="#999">30</text>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>发现时间</th>
                                <th>涉及单位</th>
                                <th>信息类型</th>
                                <th>数据泄露标题</th>
                                <th>暗网地址</th>
                                <th>风险等级</th>
                                <th>处置状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-08-24 14:23</td>
                                <td>济南市人社局</td>
                                <td>数据泄露</td>
                                <td>[URGENT] 济南市人社局社保数据库完整备份 - 包含身份证、银行卡信息</td>
                                <td>onion://7hj9f2k8...</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td><span class="status orange">处置中</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2024-08-23 19:45</td>
                                <td>济南市公共资源交易中心</td>
                                <td>账号交易</td>
                                <td>政府采购平台管理员权限账号 - 可修改招标结果</td>
                                <td>onion://3rf5d9m1...</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2024-08-23 11:12</td>
                                <td>济南市大数据局</td>
                                <td>黑客攻击</td>
                                <td>政务云平台0day漏洞利用工具包 - 含RCE Payload</td>
                                <td>onion://9ju7g4x3...</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td><span class="status red">未处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2024-08-22 16:34</td>
                                <td>济南市卫健委</td>
                                <td>数据泄露</td>
                                <td>医保系统患者隐私数据 - 包含病历、检查报告、药品记录</td>
                                <td>onion://2hy6t8k9...</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2024-08-21 09:56</td>
                                <td>济南市政务服务中心</td>
                                <td>账号交易</td>
                                <td>一网通办平台超级管理员账号 - 可访问所有部门数据</td>
                                <td>onion://5gt7y3m2...</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="internet-exposure">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="输入单位名称、IP地址或域名搜索">
                <button class="search-btn">搜索</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-title">互联网资产总数</div>
                    <div class="stat-value blue">2,847</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">数据暴露总量</div>
                    <div class="stat-value red">23</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">敏感信息暴露</div>
                    <div class="stat-value orange">18</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">已处置暴露信息</div>
                    <div class="stat-value green">15</div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-title">互联网暴露资产分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 柱状图模拟 -->
                            <rect x="50" y="50" width="40" height="150" fill="#1890ff" />
                            <rect x="100" y="80" width="40" height="120" fill="#1890ff" />
                            <rect x="150" y="100" width="40" height="100" fill="#1890ff" />
                            <rect x="200" y="70" width="40" height="130" fill="#1890ff" />
                            <rect x="250" y="110" width="40" height="90" fill="#1890ff" />
                            <rect x="300" y="130" width="40" height="70" fill="#1890ff" />
                            
                            <!-- 坐标轴 -->
                            <line x1="30" y1="200" x2="350" y2="200" stroke="#ddd" />
                            <line x1="30" y1="50" x2="30" y2="200" stroke="#ddd" />
                            
                            <!-- 标签 -->
                            <text x="70" y="220" font-size="12" fill="#666" text-anchor="middle">数据库</text>
                            <text x="120" y="220" font-size="12" fill="#666" text-anchor="middle">缓存服务</text>
                            <text x="170" y="220" font-size="12" fill="#666" text-anchor="middle">文件服务</text>
                            <text x="220" y="220" font-size="12" fill="#666" text-anchor="middle">API接口</text>
                            <text x="270" y="220" font-size="12" fill="#666" text-anchor="middle">Web应用</text>
                            <text x="320" y="220" font-size="12" fill="#666" text-anchor="middle">其他</text>
                        </svg>
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-title">漏洞风险等级分布</div>
                    <div class="chart-placeholder">
                        <div style="display: flex; align-items: center; justify-content: center; height: 250px;">
                            <!-- 饼图容器 -->
                            <div style="position: relative; width: 200px; height: 200px; border-radius: 50%; background: conic-gradient(#f5222d 0deg 280.8deg, #faad14 280.8deg 342deg, #52c41a 342deg 356.4deg, #d9d9d9 356.4deg 360deg); margin-right: 50px;">
                                <!-- 中心白色圆圈，创建甜甜圈效果 -->
                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80px; height: 80px; background: white; border-radius: 50%; border: 2px solid #eee;"></div>
                            </div>
                            
                            <!-- 图例 -->
                            <div style="margin-left: 20px;">
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div style="width: 15px; height: 15px; background-color: #f5222d; margin-right: 10px;"></div>
                                    <span style="font-size: 12px; color: #666;">高危 (78%)</span>
                                </div>
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div style="width: 15px; height: 15px; background-color: #faad14; margin-right: 10px;"></div>
                                    <span style="font-size: 12px; color: #666;">中危 (17%)</span>
                                </div>
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div style="width: 15px; height: 15px; background-color: #52c41a; margin-right: 10px;"></div>
                                    <span style="font-size: 12px; color: #666;">低危 (4%)</span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 15px; height: 15px; background-color: #d9d9d9; margin-right: 10px;"></div>
                                    <span style="font-size: 12px; color: #666;">无风险 (1%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>暴露位置</th>
                                <th>暴露内容</th>
                                <th>所属单位</th>
                                <th>暴露平台</th>
                                <th>数据量</th>
                                <th>风险等级</th>
                                <th>发现时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MongoDB数据库未授权访问</td>
                                <td>政务服务平台用户数据库 - 包含身份证、手机号等敏感信息</td>
                                <td>济南市政务服务中心</td>
                                <td>MongoDB 27017端口</td>
                                <td>2.3GB (约15万条记录)</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td>2024-08-24 10:30</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>Elasticsearch集群开放访问</td>
                                <td>招投标系统日志数据 - 含企业商业机密信息</td>
                                <td>济南市公共资源交易中心</td>
                                <td>Elasticsearch 9200端口</td>
                                <td>856MB (约8万条记录)</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td>2024-08-23 15:45</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>Redis未授权访问</td>
                                <td>政务云平台缓存数据 - 包含用户会话token和API密钥</td>
                                <td>济南市大数据局</td>
                                <td>Redis 6379端口</td>
                                <td>125MB (约2万个key)</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td>2024-08-23 09:20</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>FTP服务器匿名访问</td>
                                <td>医疗影像系统备份文件 - 包含患者CT、MRI等医疗影像</td>
                                <td>济南市卫健委</td>
                                <td>FTP 21端口</td>
                                <td>4.7GB (约3千个文件)</td>
                                <td><span class="alert-level high"></span> 高危</td>
                                <td>2024-08-22 14:15</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>Swagger API文档泄露</td>
                                <td>人社系统API接口文档 - 暴露内部接口和数据结构</td>
                                <td>济南市人社局</td>
                                <td>HTTP 8080端口</td>
                                <td>12个API接口</td>
                                <td><span class="alert-level medium"></span> 中危</td>
                                <td>2024-08-21 11:30</td>
                                <td><a href="#">详情</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="cross-border-analysis">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="输入单位名称、IP地址或目标国家/地区搜索">
                <button class="search-btn">搜索</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-title">今日异常跨境数据流量</div>
                    <div class="stat-value red">15.6 GB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">本周异常跨境数据流量</div>
                    <div class="stat-value orange">78.3 GB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">异常目标国家/地区</div>
                    <div class="stat-value blue">12</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">已处置异常事件</div>
                    <div class="stat-value green">23</div>
                </div>
            </div>

            <div class="map-container">
                <!-- 现代化世界地图 -->
                <svg class="map-overlay" viewBox="0 0 1000 500" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                    <!-- 渐变定义 -->
                    <defs>
                        <!-- 海洋渐变 -->
                        <radialGradient id="oceanGradient" cx="50%" cy="50%" r="60%">
                            <stop offset="0%" stop-color="#1e3a8a" stop-opacity="0.1"/>
                            <stop offset="100%" stop-color="#1e40af" stop-opacity="0.3"/>
                        </radialGradient>
                        
                        <!-- 陆地渐变 -->
                        <linearGradient id="landGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#f0fdf4"/>
                            <stop offset="50%" stop-color="#dcfce7"/>
                            <stop offset="100%" stop-color="#bbf7d0"/>
                        </linearGradient>
                        
                        <!-- 中国特殊渐变 -->
                        <linearGradient id="chinaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#dbeafe"/>
                            <stop offset="50%" stop-color="#bfdbfe"/>
                            <stop offset="100%" stop-color="#93c5fd"/>
                        </linearGradient>
                        
                        <!-- 阴影滤镜 -->
                        <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
                        </filter>
                        
                        <!-- 网格图案 -->
                        <pattern id="gridPattern" width="50" height="25" patternUnits="userSpaceOnUse">
                            <path d="M 50 0 L 0 0 0 25" fill="none" stroke="#e5e7eb" stroke-width="0.5" opacity="0.4"/>
                        </pattern>
                    </defs>
                    
                    <!-- 海洋背景 -->
                    <rect width="1000" height="500" fill="url(#oceanGradient)"/>
                    
                    <!-- 经纬线网格 -->
                    <rect width="1000" height="500" fill="url(#gridPattern)"/>
                    
                    <!-- 重要纬线 -->
                    <line x1="0" y1="125" x2="1000" y2="125" stroke="#6b7280" stroke-width="1" stroke-dasharray="8,4" opacity="0.6"/>
                    <line x1="0" y1="250" x2="1000" y2="250" stroke="#6b7280" stroke-width="1.5" stroke-dasharray="10,5" opacity="0.8"/>
                    <line x1="0" y1="375" x2="1000" y2="375" stroke="#6b7280" stroke-width="1" stroke-dasharray="8,4" opacity="0.6"/>
                    
                    <!-- 重要经线 -->
                    <line x1="250" y1="0" x2="250" y2="500" stroke="#6b7280" stroke-width="1" stroke-dasharray="8,4" opacity="0.6"/>
                    <line x1="500" y1="0" x2="500" y2="500" stroke="#6b7280" stroke-width="1.5" stroke-dasharray="10,5" opacity="0.8"/>
                    <line x1="750" y1="0" x2="750" y2="500" stroke="#6b7280" stroke-width="1" stroke-dasharray="8,4" opacity="0.6"/>
                    
                    <!-- 北美洲 -->
                    <path d="M 80 150 Q 100 140 140 145 Q 180 135 220 140 Q 260 135 300 145 Q 320 150 300 200 Q 280 250 240 280 Q 200 300 160 290 Q 120 280 100 250 Q 80 220 85 180 Z" 
                          fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)" opacity="0.9"/>
                    
                    <!-- 南美洲 -->
                    <path d="M 150 320 Q 180 310 200 320 Q 220 330 210 380 Q 200 420 180 440 Q 160 450 140 440 Q 130 420 135 380 Q 140 340 150 320 Z" 
                          fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)" opacity="0.9"/>
                    
                    <!-- 欧洲 -->
                    <path d="M 450 120 Q 480 115 510 120 Q 540 125 560 135 Q 570 150 565 180 Q 560 200 540 210 Q 510 215 480 210 Q 450 205 440 180 Q 435 150 450 120 Z" 
                          fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)" opacity="0.9"/>
                    
                    <!-- 非洲 -->
                    <path d="M 460 230 Q 490 225 520 235 Q 540 245 545 280 Q 550 320 540 360 Q 530 390 510 400 Q 480 405 460 395 Q 440 385 445 350 Q 450 310 455 270 Q 460 240 460 230 Z" 
                          fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)" opacity="0.9"/>
                    
                    <!-- 亚洲大陆（不含中国） -->
                    <path d="M 580 100 Q 620 95 660 100 Q 700 105 740 110 Q 780 115 800 130 Q 820 150 815 190 Q 810 230 790 260 Q 770 280 740 285 Q 700 290 660 285 Q 620 280 590 270 Q 570 250 575 210 Q 580 170 585 130 Z" 
                          fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)" opacity="0.9"/>
                    
                    <!-- 中国区域（突出显示） -->
                    <path d="M 680 160 Q 720 155 750 165 Q 770 175 775 200 Q 780 225 770 245 Q 760 260 740 265 Q 720 270 700 265 Q 680 260 670 240 Q 665 220 670 195 Q 675 175 680 160 Z" 
                          fill="url(#chinaGradient)" stroke="#2563eb" stroke-width="3" filter="url(#dropShadow)" opacity="1">
                        <animate attributeName="stroke-width" values="3;5;3" dur="3s" repeatCount="indefinite"/>
                    </path>
                    
                    <!-- 日本列岛 -->
                    <ellipse cx="800" cy="180" rx="15" ry="40" 
                             fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)"/>
                    
                    <!-- 东南亚群岛 -->
                    <ellipse cx="750" cy="320" rx="40" ry="25" 
                             fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)"/>
                    
                    <!-- 澳大利亚 -->
                    <ellipse cx="820" cy="400" rx="60" ry="30" 
                             fill="url(#landGradient)" stroke="#059669" stroke-width="2" filter="url(#dropShadow)"/>
                    
                    <!-- 英国群岛 -->
                    <circle cx="470" cy="140" r="8" 
                            fill="url(#landGradient)" stroke="#059669" stroke-width="1.5" filter="url(#dropShadow)"/>
                    
                    <!-- 现代化地理标签 -->
                    <text x="180" y="220" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#374151" text-anchor="middle">North America</text>
                    <text x="490" y="160" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#374151" text-anchor="middle">Europe</text>
                    <text x="725" y="210" font-family="Arial, sans-serif" font-size="16" font-weight="700" fill="#1e40af" text-anchor="middle">CHINA</text>
                    <text x="800" y="230" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="#374151" text-anchor="middle">Japan</text>
                    <text x="750" y="350" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="#374151" text-anchor="middle">Southeast Asia</text>
                    <text x="820" y="440" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#374151" text-anchor="middle">Australia</text>
                    
                    <!-- 装饰性边框 -->
                    <rect x="5" y="5" width="990" height="490" fill="none" stroke="#6b7280" stroke-width="2" rx="10" opacity="0.3"/>
                </svg>
                <!-- 济南市位置（中国中心） -->
                <div class="data-point china-center" style="top: 42%; left: 72.5%; background: linear-gradient(135deg, #3b82f6, #1e40af); z-index: 15; transform: translate(-50%, -50%) scale(1.5); box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 40px rgba(59, 130, 246, 0.3);" title="济南市 - 数据源点">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 8px; height: 8px; background: white; border-radius: 50%; animation: pulse 2s infinite;"></div>
                </div>
                
                <!-- 各国数据点（现代化设计） -->
                <!-- 美国（北美洲） -->
                <div class="data-point high-risk" style="top: 45%; left: 18%; background: linear-gradient(135deg, #ef4444, #dc2626); box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);" title="美国 - 数据泄露风险 (5.2GB)"></div>
                
                <!-- 英国（欧洲） -->
                <div class="data-point medium-risk" style="top: 28%; left: 47%; background: linear-gradient(135deg, #f59e0b, #d97706); box-shadow: 0 0 12px rgba(245, 158, 11, 0.6);" title="英国 - 中级风险 (2.8GB)"></div>
                
                <!-- 德国（欧洲中部） -->
                <div class="data-point medium-risk" style="top: 32%; left: 52%; background: linear-gradient(135deg, #f59e0b, #d97706); box-shadow: 0 0 12px rgba(245, 158, 11, 0.6);" title="德国 - 中级风险 (2.1GB)"></div>
                
                <!-- 俄罗斯（亚洲北部） -->
                <div class="data-point high-risk" style="top: 22%; left: 68%; background: linear-gradient(135deg, #ef4444, #dc2626); box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);" title="俄罗斯 - 高风险 (4.1GB)"></div>
                
                <!-- 日本（日本列岛） -->
                <div class="data-point low-risk" style="top: 36%; left: 80%; background: linear-gradient(135deg, #10b981, #059669); box-shadow: 0 0 10px rgba(16, 185, 129, 0.6);" title="日本 - 低风险 (0.9GB)"></div>
                
                <!-- 新加坡（东南亚） -->
                <div class="data-point medium-risk" style="top: 64%; left: 75%; background: linear-gradient(135deg, #f59e0b, #d97706); box-shadow: 0 0 12px rgba(245, 158, 11, 0.6);" title="新加坡 - 中级风险 (3.5GB)"></div>
                
                <!-- 澳大利亚（大洋洲） -->
                <div class="data-point low-risk" style="top: 80%; left: 82%; background: linear-gradient(135deg, #10b981, #059669); box-shadow: 0 0 10px rgba(16, 185, 129, 0.6);" title="澳大利亚 - 低风险 (1.2GB)"></div>
                
                <!-- 现代化数据流向线 -->
                <!-- 济南 -> 美国 -->
                <div class="modern-data-flow high-risk-flow" style="top: 42%; left: 72.5%; width: 420px; transform: rotate(-12deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(239,68,68,0.9)); height: 4px; box-shadow: 0 0 8px rgba(239,68,68,0.4);"></div>
                
                <!-- 济南 -> 英国 -->
                <div class="modern-data-flow medium-risk-flow" style="top: 42%; left: 72.5%; width: 200px; transform: rotate(-28deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(245,158,11,0.8)); height: 3px; box-shadow: 0 0 6px rgba(245,158,11,0.4);"></div>
                
                <!-- 济南 -> 德国 -->
                <div class="modern-data-flow medium-risk-flow" style="top: 42%; left: 72.5%; width: 160px; transform: rotate(-25deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(245,158,11,0.8)); height: 3px; box-shadow: 0 0 6px rgba(245,158,11,0.4);"></div>
                
                <!-- 济南 -> 俄罗斯 -->
                <div class="modern-data-flow high-risk-flow" style="top: 42%; left: 72.5%; width: 180px; transform: rotate(-45deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(239,68,68,0.9)); height: 4px; box-shadow: 0 0 8px rgba(239,68,68,0.4);"></div>
                
                <!-- 济南 -> 日本 -->
                <div class="modern-data-flow low-risk-flow" style="top: 42%; left: 72.5%; width: 90px; transform: rotate(-12deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(16,185,129,0.7)); height: 2px; box-shadow: 0 0 4px rgba(16,185,129,0.3);"></div>
                
                <!-- 济南 -> 新加坡 -->
                <div class="modern-data-flow medium-risk-flow" style="top: 42%; left: 72.5%; width: 180px; transform: rotate(48deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(245,158,11,0.8)); height: 3px; box-shadow: 0 0 6px rgba(245,158,11,0.4);"></div>
                
                <!-- 济南 -> 澳大利亚 -->
                <div class="modern-data-flow low-risk-flow" style="top: 42%; left: 72.5%; width: 280px; transform: rotate(62deg); background: linear-gradient(90deg, rgba(59,130,246,0.8), rgba(16,185,129,0.7)); height: 2px; box-shadow: 0 0 4px rgba(16,185,129,0.3);"></div>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-title">异常跨境数据流量趋势（近7天）</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 500 250">
                            <!-- 坐标轴 -->
                            <line x1="50" y1="200" x2="450" y2="200" stroke="#ddd" stroke-width="1" />
                            <line x1="50" y1="50" x2="50" y2="200" stroke="#ddd" stroke-width="1" />
                            
                            <!-- 折线图 -->
                            <polyline points="50,150 110,160 170,130 230,170 290,100 350,80 410,120" 
                                    fill="none" stroke="#f5222d" stroke-width="2" />
                            
                            <!-- 数据点 -->
                            <circle cx="50" cy="150" r="3" fill="#f5222d" />
                            <circle cx="110" cy="160" r="3" fill="#f5222d" />
                            <circle cx="170" cy="130" r="3" fill="#f5222d" />
                            <circle cx="230" cy="170" r="3" fill="#f5222d" />
                            <circle cx="290" cy="100" r="3" fill="#f5222d" />
                            <circle cx="350" cy="80" r="3" fill="#f5222d" />
                            <circle cx="410" cy="120" r="3" fill="#f5222d" />
                            
                            <!-- 坐标轴标签 -->
                            <text x="50" y="220" font-size="10" text-anchor="middle" fill="#999">6/9</text>
                            <text x="110" y="220" font-size="10" text-anchor="middle" fill="#999">6/10</text>
                            <text x="170" y="220" font-size="10" text-anchor="middle" fill="#999">6/11</text>
                            <text x="230" y="220" font-size="10" text-anchor="middle" fill="#999">6/12</text>
                            <text x="290" y="220" font-size="10" text-anchor="middle" fill="#999">6/13</text>
                            <text x="350" y="220" font-size="10" text-anchor="middle" fill="#999">6/14</text>
                            <text x="410" y="220" font-size="10" text-anchor="middle" fill="#999">6/15</text>
                            
                            <text x="40" y="200" font-size="10" text-anchor="end" fill="#999">0</text>
                            <text x="40" y="150" font-size="10" text-anchor="end" fill="#999">10GB</text>
                            <text x="40" y="100" font-size="10" text-anchor="end" fill="#999">20GB</text>
                            <text x="40" y="50" font-size="10" text-anchor="end" fill="#999">30GB</text>
                        </svg>
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-title">异常跨境数据目标国家/地区分布</div>
                    <div class="chart-placeholder">
                        <svg width="100%" height="100%" viewBox="0 0 400 250">
                            <!-- 柱状图模拟 -->
                            <rect x="50" y="80" width="30" height="120" fill="#f5222d" />
                            <rect x="90" y="110" width="30" height="90" fill="#f5222d" />
                            <rect x="130" y="130" width="30" height="70" fill="#faad14" />
                            <rect x="170" y="140" width="30" height="60" fill="#faad14" />
                            <rect x="210" y="150" width="30" height="50" fill="#1890ff" />
                            <rect x="250" y="160" width="30" height="40" fill="#1890ff" />
                            <rect x="290" y="170" width="30" height="30" fill="#52c41a" />
                            <rect x="330" y="180" width="30" height="20" fill="#52c41a" />
                            
                            <!-- 坐标轴 -->
                            <line x1="30" y1="200" x2="370" y2="200" stroke="#ddd" />
                            <line x1="30" y1="80" x2="30" y2="200" stroke="#ddd" />
                            
                            <!-- 标签 -->
                            <text x="65" y="220" font-size="10" fill="#666" text-anchor="middle">美国</text>
                            <text x="105" y="220" font-size="10" fill="#666" text-anchor="middle">俄罗斯</text>
                            <text x="145" y="220" font-size="10" fill="#666" text-anchor="middle">新加坡</text>
                            <text x="185" y="220" font-size="10" fill="#666" text-anchor="middle">英国</text>
                            <text x="225" y="220" font-size="10" fill="#666" text-anchor="middle">德国</text>
                            <text x="265" y="220" font-size="10" fill="#666" text-anchor="middle">日本</text>
                            <text x="305" y="220" font-size="10" fill="#666" text-anchor="middle">澳大利亚</text>
                            <text x="345" y="220" font-size="10" fill="#666" text-anchor="middle">其他</text>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>发生时间</th>
                                <th>源IP</th>
                                <th>目标国家/地区</th>
                                <th>目标IP</th>
                                <th>数据量</th>
                                <th>所属单位</th>
                                <th>处置状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2023-06-15 09:23</td>
                                <td>172.16.xx.xx</td>
                                <td>美国</td>
                                <td>104.xx.xx.xx</td>
                                <td>2.3 GB</td>
                                <td>济南市大数据局</td>
                                <td><span class="status red">未处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-15 08:12</td>
                                <td>172.16.xx.xx</td>
                                <td>俄罗斯</td>
                                <td>95.xx.xx.xx</td>
                                <td>1.8 GB</td>
                                <td>济南市政务服务中心</td>
                                <td><span class="status orange">处置中</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-14 23:45</td>
                                <td>172.16.xx.xx</td>
                                <td>新加坡</td>
                                <td>128.xx.xx.xx</td>
                                <td>3.5 GB</td>
                                <td>济南市人社局</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-14 16:34</td>
                                <td>172.16.xx.xx</td>
                                <td>英国</td>
                                <td>82.xx.xx.xx</td>
                                <td>1.2 GB</td>
                                <td>济南市公共资源交易中心</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                            <tr>
                                <td>2023-06-13 14:22</td>
                                <td>172.16.xx.xx</td>
                                <td>澳大利亚</td>
                                <td>203.xx.xx.xx</td>
                                <td>0.8 GB</td>
                                <td>济南市卫健委</td>
                                <td><span class="status green">已处置</span></td>
                                <td><a href="#">详情</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的标签页切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            const submenuItems = document.querySelectorAll('.submenu-item');
            
            tabs.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 设置当前标签为活动状态
                    tab.classList.add('active');
                    tabContents[index].classList.add('active');
                    
                    // 同步侧边栏菜单
                    submenuItems.forEach((item, i) => {
                        item.classList.remove('active');
                        if (i === index) {
                            item.classList.add('active');
                        }
                    });
                });
            });
            
            // 侧边栏子菜单点击
            submenuItems.forEach((item, index) => {
                item.addEventListener('click', () => {
                    // 移除所有活动状态
                    submenuItems.forEach(i => i.classList.remove('active'));
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 设置当前菜单和对应标签为活动状态
                    item.classList.add('active');
                    tabs[index].classList.add('active');
                    tabContents[index].classList.add('active');
                });
            });
        });
    </script>
</body>
</html>