<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Data Transfer Command Center</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            color: #333;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            z-index: 1000;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .logo {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
        }

        .nav-item {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .nav-item:hover, .nav-item.active {
            color: #1890ff;
            background: #e6f7ff;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .clock {
            font-size: 14px;
            color: #999;
        }

        /* 主容器 */
        .dashboard {
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            background: #f5f7fa;
        }

        .page-header {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 统计卡片区域 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #1890ff;
        }

        .stat-card.warning::before {
            background: #faad14;
        }

        .stat-card.danger::before {
            background: #ff4d4f;
        }

        .stat-card.success::before {
            background: #52c41a;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }

        .trend-up {
            color: #52c41a;
        }

        .trend-down {
            color: #ff4d4f;
        }

        /* 内容区域 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 24px;
            margin-bottom: 24px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        /* 地球和实时监控 */
        .globe-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .globe {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
            margin: 0 auto 16px;
            position: relative;
            animation: rotate 20s linear infinite;
            box-shadow: 0 8px 32px rgba(24,144,255,0.3);
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .globe::before {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            width: 80%;
            height: 80%;
            border-radius: 50%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 8px,
                rgba(255,255,255,0.1) 8px,
                rgba(255,255,255,0.1) 16px
            );
        }

        .traffic-display {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .traffic-label {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 热力图 */
        .heatmap-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
        }

        .heatmap {
            display: flex;
            flex-direction: column;
            gap: 4px;
            height: 300px;
        }

        .heat-bar {
            height: 12px;
            background: #f0f0f0;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .heat-bar::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #52c41a);
            border-radius: 6px;
            width: var(--intensity, 30%);
            transition: width 0.3s ease;
        }

        .heat-bar:hover::after {
            background: linear-gradient(90deg, #40a9ff, #73d13d);
        }

        /* 图表容器 */
        .chart-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart {
            height: 200px;
            display: flex;
            align-items: end;
            gap: 3px;
            padding: 16px 0;
        }

        .chart-bar {
            flex: 1;
            border-radius: 4px 4px 0 0;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .chart-bar:hover {
            opacity: 0.8;
            transform: translateY(-2px);
        }

        .chart-bar.hr { background: #1890ff; }
        .chart-bar.crm { background: #52c41a; }
        .chart-bar.iot { background: #faad14; }
        .chart-bar.log { background: #f759ab; }
        .chart-bar.other { background: #722ed1; }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-top: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #666;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        /* 事件列表 */
        .events-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .event-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .event-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s;
        }

        .event-item:hover {
            background: #fafafa;
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-flag {
            font-size: 20px;
        }

        .event-content {
            flex: 1;
        }

        .event-time {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
        }

        .event-desc {
            font-size: 14px;
            color: #666;
        }

        /* 雷达图 */
        .radar-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .radar-chart {
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        /* 饼图容器 */
        .pie-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .pie-chart {
            width: 200px;
            height: 200px;
            margin: 0 auto 16px;
            border-radius: 50%;
            background: conic-gradient(
                #1890ff 0deg 72deg,
                #52c41a 72deg 144deg,
                #faad14 144deg 216deg,
                #f759ab 216deg 288deg,
                #722ed1 288deg 360deg
            );
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="nav-menu">
            <div class="logo">数据安全监管平台</div>
            <a href="#" class="nav-item active">资产台账</a>
            <a href="#" class="nav-item">数据监控</a>
            <a href="#" class="nav-item">威胁监测</a>
            <a href="#" class="nav-item">安全事件</a>
            <a href="#" class="nav-item">安全评估</a>
            <a href="#" class="nav-item">法规管理</a>
            <a href="#" class="nav-item">信息管理</a>
            <a href="#" class="nav-item">考核评价</a>
            <a href="#" class="nav-item">事件统计</a>
            <a href="#" class="nav-item">数据分析</a>
            <a href="#" class="nav-item">安全对接</a>
            <a href="#" class="nav-item">决策指挥</a>
            <a href="#" class="nav-item">系统管理</a>
        </div>
        <div class="user-info">
            <div class="clock" id="clock"></div>
            <span>admin</span>
        </div>
    </div>

    <!-- 主仪表板 -->
    <div class="dashboard">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">Global Data Transfer Command Center</div>
            <div class="page-subtitle">全球数据跨境传输指挥中心 - 实时监控与合规管理</div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-files">1,247</div>
                <div class="stat-label">总档案数</div>
                <div class="stat-trend trend-up">↗ +12.5%</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="new-files">43</div>
                <div class="stat-label">本月新增</div>
                <div class="stat-trend trend-up">↗ +8.3%</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-value" id="pending">12</div>
                <div class="stat-label">待审批</div>
                <div class="stat-trend">→ 0%</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-value" id="expiring">7</div>
                <div class="stat-label">即将到期</div>
                <div class="stat-trend trend-down">↘ -2</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-value" id="blocked">3</div>
                <div class="stat-label">阻断事件</div>
                <div class="stat-trend trend-down">↘ -1</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-value" id="high-risk">4</div>
                <div class="stat-label">高风险国家</div>
                <div class="stat-trend">→ 0</div>
            </div>
            <div class="stat-card success">
                <div class="stat-value" id="compliance-score">87</div>
                <div class="stat-label">平均合规得分</div>
                <div class="stat-trend trend-up">↗ +2.1</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="transfer-rate">2.3</div>
                <div class="stat-label">实时传输速率(Gbps)</div>
                <div class="stat-trend trend-up">↗ +0.5</div>
            </div>
        </div>

        <!-- 内容网格 -->
        <div class="content-grid">
            <!-- 主要内容区 -->
            <div class="main-content">
                <!-- 30天数据分类条形图 -->
                <div class="chart-container">
                    <div class="section-title">近30天跨境数据分类统计</div>
                    <div class="chart" id="chart"></div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1890ff;"></div>
                            <span>HR数据</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #52c41a;"></div>
                            <span>CRM数据</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #faad14;"></div>
                            <span>IoT数据</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #f759ab;"></div>
                            <span>日志数据</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #722ed1;"></div>
                            <span>其他数据</span>
                        </div>
                    </div>
                </div>

                <!-- 合规事件列表 -->
                <div class="events-container">
                    <div class="section-title">最近合规事件</div>
                    <div class="event-list" id="event-list">
                        <div class="event-item">
                            <div class="event-flag">🇺🇸</div>
                            <div class="event-content">
                                <div class="event-time">14:23</div>
                                <div class="event-desc">美国数据传输合规检查通过</div>
                            </div>
                        </div>
                        <div class="event-item">
                            <div class="event-flag">🇯🇵</div>
                            <div class="event-content">
                                <div class="event-time">14:15</div>
                                <div class="event-desc">日本IoT数据同步完成</div>
                            </div>
                        </div>
                        <div class="event-item">
                            <div class="event-flag">🇩🇪</div>
                            <div class="event-content">
                                <div class="event-time">14:08</div>
                                <div class="event-desc">德国CRM数据审批中</div>
                            </div>
                        </div>
                        <div class="event-item">
                            <div class="event-flag">🇬🇧</div>
                            <div class="event-content">
                                <div class="event-time">14:02</div>
                                <div class="event-desc">英国HR数据加密传输</div>
                            </div>
                        </div>
                        <div class="event-item">
                            <div class="event-flag">🇫🇷</div>
                            <div class="event-content">
                                <div class="event-time">13:58</div>
                                <div class="event-desc">法国日志数据备份完成</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 3D地球 -->
                <div class="globe-container">
                    <div class="section-title">实时传输监控</div>
                    <div class="globe"></div>
                    <div class="traffic-display">8.7 TB</div>
                    <div class="traffic-label">今日传输流量</div>
                </div>

                <!-- 24小时热力图 -->
                <div class="heatmap-container">
                    <div class="section-title">24小时数据量热力</div>
                    <div class="heatmap" id="heatmap"></div>
                </div>

                <!-- 数据库分布饼图 -->
                <div class="pie-container">
                    <div class="section-title">数据库类型分布</div>
                    <div class="pie-chart"></div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1890ff;"></div>
                            <span>Oracle</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #52c41a;"></div>
                            <span>MySQL</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #faad14;"></div>
                            <span>MongoDB</span>
                        </div>
                    </div>
                </div>

                <!-- 风险雷达图 -->
                <div class="radar-container">
                    <div class="section-title">风险评估雷达</div>
                    <div class="radar-chart">
                        <svg viewBox="0 0 200 200">
                            <defs>
                                <radialGradient id="radarGrad">
                                    <stop offset="0%" style="stop-color:#1890ff;stop-opacity:0.3"/>
                                    <stop offset="100%" style="stop-color:#1890ff;stop-opacity:0"/>
                                </radialGradient>
                            </defs>
                            <!-- 雷达网格 -->
                            <circle cx="100" cy="100" r="80" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <circle cx="100" cy="100" r="60" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <circle cx="100" cy="100" r="40" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <circle cx="100" cy="100" r="20" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <!-- 雷达线 -->
                            <line x1="100" y1="20" x2="100" y2="180" stroke="#d9d9d9" stroke-width="1"/>
                            <line x1="20" y1="100" x2="180" y2="100" stroke="#d9d9d9" stroke-width="1"/>
                            <line x1="42" y1="42" x2="158" y2="158" stroke="#d9d9d9" stroke-width="1"/>
                            <line x1="158" y1="42" x2="42" y2="158" stroke="#d9d9d9" stroke-width="1"/>
                            <!-- 数据多边形 -->
                            <polygon points="100,40 140,60 150,100 120,140 80,130" fill="url(#radarGrad)" stroke="#1890ff" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 实时时钟
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('clock').textContent = timeString;
        }

        // 生成热力图
        function generateHeatmap() {
            const heatmap = document.getElementById('heatmap');
            heatmap.innerHTML = ''; // 清空现有内容

            for (let i = 0; i < 24; i++) {
                const bar = document.createElement('div');
                bar.className = 'heat-bar';
                const intensity = Math.random() * 100 + 20;
                bar.style.setProperty('--intensity', intensity + '%');
                bar.title = `${i}:00 - ${intensity.toFixed(1)}GB`;
                heatmap.appendChild(bar);
            }
        }

        // 生成条形图
        function generateChart() {
            const chart = document.getElementById('chart');
            chart.innerHTML = ''; // 清空现有内容

            const categories = ['hr', 'crm', 'iot', 'log', 'other'];

            for (let i = 0; i < 30; i++) {
                const bar = document.createElement('div');
                bar.className = `chart-bar ${categories[i % 5]}`;
                const height = Math.random() * 150 + 30;
                bar.style.height = height + 'px';
                bar.title = `第${i+1}天`;
                chart.appendChild(bar);
            }
        }

        // 数据刷新
        function refreshData() {
            // 模拟数据更新
            const kpis = ['total-files', 'new-files', 'pending', 'expiring', 'blocked', 'high-risk', 'compliance-score', 'transfer-rate'];
            kpis.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    const currentValue = parseInt(element.textContent.replace(/,/g, '')) || 0;
                    const change = Math.floor(Math.random() * 10 - 5);
                    const newValue = Math.max(0, currentValue + change);

                    // 格式化数字显示
                    if (id === 'transfer-rate') {
                        element.textContent = (newValue / 1000).toFixed(1);
                    } else if (newValue >= 1000) {
                        element.textContent = newValue.toLocaleString();
                    } else {
                        element.textContent = newValue;
                    }
                }
            });

            // 刷新图表
            generateHeatmap();
            generateChart();
        }

        // 添加新事件到列表
        function addNewEvent() {
            const events = [
                { flag: '🇺🇸', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '美国数据传输合规检查通过' },
                { flag: '🇯🇵', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '日本IoT数据同步完成' },
                { flag: '🇩🇪', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '德国CRM数据审批中' },
                { flag: '🇬🇧', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '英国HR数据加密传输' },
                { flag: '🇫🇷', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '法国日志数据备份完成' },
                { flag: '🇨🇦', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '加拿大数据库连接测试' },
                { flag: '🇦🇺', time: new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}), desc: '澳大利亚数据同步完成' }
            ];

            const eventList = document.getElementById('event-list');
            const randomEvent = events[Math.floor(Math.random() * events.length)];

            const eventItem = document.createElement('div');
            eventItem.className = 'event-item';
            eventItem.innerHTML = `
                <div class="event-flag">${randomEvent.flag}</div>
                <div class="event-content">
                    <div class="event-time">${randomEvent.time}</div>
                    <div class="event-desc">${randomEvent.desc}</div>
                </div>
            `;

            eventList.insertBefore(eventItem, eventList.firstChild);

            // 保持最多10个事件
            while (eventList.children.length > 10) {
                eventList.removeChild(eventList.lastChild);
            }
        }

        // 初始化
        window.addEventListener('load', () => {
            generateHeatmap();
            generateChart();
            updateClock();
            setInterval(updateClock, 1000);
            setInterval(refreshData, 30000); // 30秒刷新数据
            setInterval(addNewEvent, 45000); // 45秒添加新事件
        });
    </script>
</body>
</html>
