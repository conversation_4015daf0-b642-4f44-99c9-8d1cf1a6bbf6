<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Data Transfer Command Center</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            color: #00ff88;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        /* 粒子背景 */
        #particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 80px;
            background: linear-gradient(90deg, rgba(0,255,136,0.1) 0%, rgba(0,136,255,0.1) 100%);
            border-bottom: 2px solid #00ff88;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 40px;
            z-index: 1000;
        }

        .title {
            font-size: 32px;
            font-weight: bold;
            text-shadow: 0 0 20px #00ff88;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px #00ff88; }
            to { text-shadow: 0 0 30px #00ff88, 0 0 40px #00ff88; }
        }

        .clock {
            font-size: 24px;
            color: #00aaff;
            text-shadow: 0 0 10px #00aaff;
        }

        /* 主容器 */
        .dashboard {
            position: absolute;
            top: 80px;
            left: 0;
            width: 100%;
            height: calc(100vh - 80px);
            display: grid;
            grid-template-columns: 400px 1fr 200px;
            grid-template-rows: 300px 1fr 200px;
            gap: 20px;
            padding: 20px;
        }

        /* 3D地球容器 */
        .globe-container {
            grid-row: 1 / 3;
            position: relative;
            background: rgba(0,255,136,0.05);
            border: 1px solid #00ff88;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .globe {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(0,255,136,0.3), rgba(0,136,255,0.1));
            border: 2px solid #00ff88;
            position: relative;
            animation: rotate 20s linear infinite;
            box-shadow: 0 0 50px rgba(0,255,136,0.3);
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .globe::before {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            width: 80%;
            height: 80%;
            border-radius: 50%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(0,255,136,0.1) 10px,
                rgba(0,255,136,0.1) 20px
            );
        }

        .traffic-display {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            padding: 10px 20px;
            border: 1px solid #00ff88;
            border-radius: 5px;
            font-size: 18px;
            text-shadow: 0 0 10px #00ff88;
        }

        /* KPI指标区域 */
        .kpi-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 15px;
        }

        .kpi-card {
            background: rgba(0,255,136,0.05);
            border: 1px solid #00ff88;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,255,136,0.2), transparent);
            animation: scan 3s linear infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .kpi-value {
            font-size: 28px;
            font-weight: bold;
            color: #00aaff;
            text-shadow: 0 0 15px #00aaff;
            margin-bottom: 5px;
        }

        .kpi-label {
            font-size: 12px;
            color: #888;
        }

        /* 热力条 */
        .heatmap-container {
            grid-row: 1 / 3;
            background: rgba(0,255,136,0.05);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
        }

        .heatmap-title {
            text-align: center;
            margin-bottom: 20px;
            color: #00ff88;
            font-size: 14px;
        }

        .heatmap {
            display: flex;
            flex-direction: column;
            height: calc(100% - 40px);
            gap: 2px;
        }

        .heat-bar {
            height: 20px;
            background: linear-gradient(90deg, #001122, #003344, #005566, #007788, #00aacc);
            border-radius: 2px;
            position: relative;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* 条形图容器 */
        .chart-container {
            grid-column: 1 / 4;
            background: rgba(0,255,136,0.05);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
        }

        .chart-title {
            text-align: center;
            margin-bottom: 15px;
            color: #00ff88;
            font-size: 16px;
        }

        .chart {
            height: 120px;
            display: flex;
            align-items: end;
            gap: 2px;
        }

        .chart-bar {
            flex: 1;
            background: linear-gradient(to top, #ff4444, #ffaa44, #44ff44, #4444ff, #aa44ff);
            border-radius: 2px 2px 0 0;
            animation: grow 1s ease-out;
        }

        @keyframes grow {
            from { height: 0; }
            to { height: var(--height); }
        }

        /* 跑马灯 */
        .marquee-container {
            grid-column: 1 / 3;
            background: rgba(0,255,136,0.05);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 15px;
            overflow: hidden;
        }

        .marquee {
            white-space: nowrap;
            animation: scroll 30s linear infinite;
            font-size: 14px;
        }

        @keyframes scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }

        /* 雷达图 */
        .radar-container {
            background: rgba(0,255,136,0.05);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .radar-chart {
            width: 150px;
            height: 150px;
            position: relative;
        }

        .radar-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* 全屏按钮 */
        .fullscreen-btn {
            position: fixed;
            top: 100px;
            right: 20px;
            background: rgba(0,255,136,0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1001;
            transition: all 0.3s;
        }

        .fullscreen-btn:hover {
            background: rgba(0,255,136,0.4);
            box-shadow: 0 0 20px rgba(0,255,136,0.5);
        }
    </style>
</head>
<body>
    <!-- 粒子背景 -->
    <canvas id="particles"></canvas>

    <!-- 顶部标题栏 -->
    <div class="header">
        <div class="title">Global Data Transfer Command Center</div>
        <div class="clock" id="clock"></div>
    </div>

    <!-- 全屏按钮 -->
    <button class="fullscreen-btn" onclick="toggleFullscreen()">全屏</button>

    <!-- 主仪表板 -->
    <div class="dashboard">
        <!-- 3D地球 -->
        <div class="globe-container">
            <div class="globe">
                <div class="traffic-display">今日流量: 8.7 TB</div>
            </div>
        </div>

        <!-- KPI指标 -->
        <div class="kpi-container">
            <div class="kpi-card">
                <div class="kpi-value" id="total-files">1,247</div>
                <div class="kpi-label">总档案数</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="new-files">43</div>
                <div class="kpi-label">本月新增</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="pending">12</div>
                <div class="kpi-label">待审批</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="expiring">7</div>
                <div class="kpi-label">即将到期</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="blocked" style="color: #ff4444;">3</div>
                <div class="kpi-label">阻断事件</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="high-risk" style="color: #ffaa44;">4</div>
                <div class="kpi-label">高风险国家</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="compliance-score">87</div>
                <div class="kpi-label">平均合规得分</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value" id="transfer-rate">2.3</div>
                <div class="kpi-label">实时传输速率(Gbps)</div>
            </div>
        </div>

        <!-- 24小时热力图 -->
        <div class="heatmap-container">
            <div class="heatmap-title">24小时数据量热力</div>
            <div class="heatmap" id="heatmap"></div>
        </div>

        <!-- 30天数据分类条形图 -->
        <div class="chart-container">
            <div class="chart-title">近30天跨境数据分类统计</div>
            <div class="chart" id="chart"></div>
        </div>

        <!-- 合规事件跑马灯 -->
        <div class="marquee-container">
            <div class="marquee" id="marquee">
                🇺🇸 14:23 美国数据传输合规检查通过 | 🇯🇵 14:15 日本IoT数据同步完成 | 🇩🇪 14:08 德国CRM数据审批中 | 🇬🇧 14:02 英国HR数据加密传输 | 🇫🇷 13:58 法国日志数据备份完成
            </div>
        </div>

        <!-- 风险雷达图 -->
        <div class="radar-container">
            <div class="radar-chart">
                <svg class="radar-bg" viewBox="0 0 150 150">
                    <defs>
                        <radialGradient id="radarGrad">
                            <stop offset="0%" style="stop-color:#00ff88;stop-opacity:0.3"/>
                            <stop offset="100%" style="stop-color:#00ff88;stop-opacity:0"/>
                        </radialGradient>
                    </defs>
                    <!-- 雷达网格 -->
                    <circle cx="75" cy="75" r="60" fill="none" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <circle cx="75" cy="75" r="40" fill="none" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <circle cx="75" cy="75" r="20" fill="none" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <!-- 雷达线 -->
                    <line x1="75" y1="15" x2="75" y2="135" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <line x1="15" y1="75" x2="135" y2="75" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <line x1="32" y1="32" x2="118" y2="118" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <line x1="118" y1="32" x2="32" y2="118" stroke="#00ff88" stroke-width="1" opacity="0.3"/>
                    <!-- 数据多边形 -->
                    <polygon points="75,25 105,45 115,75 95,105 55,95" fill="url(#radarGrad)" stroke="#00ff88" stroke-width="2"/>
                </svg>
            </div>
        </div>
    </div>

    <script>
        // 粒子背景动画
        function initParticles() {
            const canvas = document.getElementById('particles');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            const particles = [];
            for (let i = 0; i < 100; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 2 + 1
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'rgba(0, 255, 136, 0.1)';
                
                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                    
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fill();
                });
                
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 实时时钟
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('clock').textContent = timeString;
        }

        // 生成热力图
        function generateHeatmap() {
            const heatmap = document.getElementById('heatmap');
            for (let i = 0; i < 24; i++) {
                const bar = document.createElement('div');
                bar.className = 'heat-bar';
                const intensity = Math.random();
                bar.style.background = `linear-gradient(90deg, 
                    rgba(0,17,34,${intensity * 0.5}), 
                    rgba(0,170,204,${intensity}))`;
                heatmap.appendChild(bar);
            }
        }

        // 生成条形图
        function generateChart() {
            const chart = document.getElementById('chart');
            const categories = ['HR', 'CRM', 'IoT', 'Log', '其他'];
            const colors = ['#ff4444', '#ffaa44', '#44ff44', '#4444ff', '#aa44ff'];
            
            for (let i = 0; i < 30; i++) {
                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                const height = Math.random() * 100 + 20;
                bar.style.setProperty('--height', height + 'px');
                bar.style.height = height + 'px';
                bar.style.background = colors[i % 5];
                chart.appendChild(bar);
            }
        }

        // 数据刷新
        function refreshData() {
            // 模拟数据更新
            const kpis = ['total-files', 'new-files', 'pending', 'expiring', 'blocked', 'high-risk', 'compliance-score', 'transfer-rate'];
            kpis.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    const currentValue = parseInt(element.textContent) || 0;
                    const newValue = currentValue + Math.floor(Math.random() * 10 - 5);
                    element.textContent = Math.max(0, newValue);
                }
            });
        }

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 初始化
        window.addEventListener('load', () => {
            initParticles();
            generateHeatmap();
            generateChart();
            updateClock();
            setInterval(updateClock, 1000);
            setInterval(refreshData, 30000); // 30秒刷新
        });

        // 响应式调整
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('particles');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
