<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Data Transfer Command Center</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            color: #333;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            z-index: 1000;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .logo {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
        }

        .nav-item {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .nav-item:hover, .nav-item.active {
            color: #1890ff;
            background: #e6f7ff;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .clock {
            font-size: 14px;
            color: #999;
        }

        /* 主容器 */
        .dashboard {
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            background: #f5f7fa;
        }

        .page-header {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 统计卡片区域 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #1890ff;
        }

        .stat-card.warning::before {
            background: #faad14;
        }

        .stat-card.danger::before {
            background: #ff4d4f;
        }

        .stat-card.success::before {
            background: #52c41a;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }

        .trend-up {
            color: #52c41a;
        }

        .trend-down {
            color: #ff4d4f;
        }

        /* 内容区域 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 400px;
            gap: 24px;
            margin-bottom: 24px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .middle-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        /* 地球和实时监控 */
        .globe-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .globe {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
            margin: 0 auto 16px;
            position: relative;
            animation: rotate 20s linear infinite;
            box-shadow: 0 8px 32px rgba(24,144,255,0.3);
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .globe::before {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            width: 80%;
            height: 80%;
            border-radius: 50%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 8px,
                rgba(255,255,255,0.1) 8px,
                rgba(255,255,255,0.1) 16px
            );
        }

        .traffic-display {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .traffic-label {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 热力图 */
        .heatmap-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
        }

        .heatmap {
            display: flex;
            flex-direction: column;
            gap: 4px;
            height: 300px;
        }

        .heat-bar {
            height: 12px;
            background: #f0f0f0;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .heat-bar::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #52c41a);
            border-radius: 6px;
            width: var(--intensity, 30%);
            transition: width 0.3s ease;
        }

        .heat-bar:hover::after {
            background: linear-gradient(90deg, #40a9ff, #73d13d);
        }

        /* 图表容器 */
        .chart-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart {
            height: 200px;
            display: flex;
            align-items: end;
            gap: 3px;
            padding: 16px 0;
        }

        .chart-bar {
            flex: 1;
            border-radius: 4px 4px 0 0;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .chart-bar:hover {
            opacity: 0.8;
            transform: translateY(-2px);
        }

        .chart-bar.hr { background: #1890ff; }
        .chart-bar.crm { background: #52c41a; }
        .chart-bar.iot { background: #faad14; }
        .chart-bar.log { background: #f759ab; }
        .chart-bar.other { background: #722ed1; }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-top: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #666;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        /* 事件列表 */
        .events-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .event-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .event-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s;
        }

        .event-item:hover {
            background: #fafafa;
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-flag {
            font-size: 20px;
        }

        .event-content {
            flex: 1;
        }

        .event-time {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
        }

        .event-desc {
            font-size: 14px;
            color: #666;
        }

        /* 雷达图 */
        .radar-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .radar-chart {
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        /* 饼图容器 */
        .pie-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .pie-chart {
            width: 200px;
            height: 200px;
            margin: 0 auto 16px;
            border-radius: 50%;
            background: conic-gradient(
                #1890ff 0deg 72deg,
                #52c41a 72deg 144deg,
                #faad14 144deg 216deg,
                #f759ab 216deg 288deg,
                #722ed1 288deg 360deg
            );
        }

        /* 网络拓扑图 */
        .topology-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            height: 300px;
        }

        .topology-map {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f5ff 100%);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .network-node {
            position: absolute;
            width: 80px;
            height: 50px;
            background: #52c41a;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(82,196,26,0.3);
            cursor: pointer;
            transition: all 0.3s;
        }

        .network-node:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 16px rgba(82,196,26,0.5);
        }

        .network-node.database {
            background: #1890ff;
            box-shadow: 0 2px 8px rgba(24,144,255,0.3);
        }

        .network-node.server {
            background: #722ed1;
            box-shadow: 0 2px 8px rgba(114,46,209,0.3);
        }

        .network-node.firewall {
            background: #fa541c;
            box-shadow: 0 2px 8px rgba(250,84,28,0.3);
        }

        .connection-line {
            position: absolute;
            height: 2px;
            background: #d9d9d9;
            transform-origin: left center;
        }

        .connection-line.active {
            background: #52c41a;
            animation: dataFlow 2s linear infinite;
        }

        @keyframes dataFlow {
            0% { box-shadow: 0 0 0 rgba(82,196,26,0.8); }
            50% { box-shadow: 10px 0 20px rgba(82,196,26,0.4); }
            100% { box-shadow: 0 0 0 rgba(82,196,26,0.8); }
        }

        /* 趋势图 */
        .trend-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .trend-chart {
            height: 200px;
            position: relative;
            background: #fafafa;
            border-radius: 4px;
            padding: 16px;
        }

        .trend-line {
            position: absolute;
            bottom: 16px;
            left: 16px;
            right: 16px;
            height: 2px;
            background: #ff4d4f;
        }

        .trend-point {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #ff4d4f;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        /* 数据表格 */
        .data-table {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }

        .table td {
            color: #666;
        }

        .table tr:hover {
            background: #fafafa;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-badge.warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }

        .status-badge.danger {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        /* 响应式 */
        @media (max-width: 1400px) {
            .content-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="nav-menu">
            <div class="logo">数据安全监管平台</div>
            <a href="#" class="nav-item active">资产台账</a>
            <a href="#" class="nav-item">数据监控</a>
            <a href="#" class="nav-item">威胁监测</a>
            <a href="#" class="nav-item">安全事件</a>
            <a href="#" class="nav-item">安全评估</a>
            <a href="#" class="nav-item">法规管理</a>
            <a href="#" class="nav-item">信息管理</a>
            <a href="#" class="nav-item">考核评价</a>
            <a href="#" class="nav-item">事件统计</a>
            <a href="#" class="nav-item">数据分析</a>
            <a href="#" class="nav-item">安全对接</a>
            <a href="#" class="nav-item">决策指挥</a>
            <a href="#" class="nav-item">系统管理</a>
        </div>
        <div class="user-info">
            <div class="clock" id="clock"></div>
            <span>admin</span>
        </div>
    </div>

    <!-- 主仪表板 -->
    <div class="dashboard">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">Global Data Transfer Command Center</div>
            <div class="page-subtitle">全球数据跨境传输指挥中心 - 实时监控与合规管理</div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-assets">15.6</div>
                <div class="stat-label">资产总数(万)</div>
                <div class="stat-trend trend-up">↗ +12.5%</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-value" id="risk-assets">17</div>
                <div class="stat-label">风险资产数</div>
                <div class="stat-trend trend-down">↘ -3</div>
            </div>
            <div class="stat-card success">
                <div class="stat-value" id="compliance-rate">60.0%</div>
                <div class="stat-label">合规率</div>
                <div class="stat-trend trend-up">↗ +5.2%</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="scan-count">31</div>
                <div class="stat-label">扫描次数</div>
                <div class="stat-trend trend-up">↗ +8</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-value" id="vulnerability-count">76.1</div>
                <div class="stat-label">漏洞数量(个)</div>
                <div class="stat-trend trend-down">↘ -12</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-value" id="high-risk-vuln">12</div>
                <div class="stat-label">高危漏洞</div>
                <div class="stat-trend trend-down">↘ -2</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="medium-risk-vuln">35</div>
                <div class="stat-label">中危漏洞</div>
                <div class="stat-trend">→ 0</div>
            </div>
            <div class="stat-card success">
                <div class="stat-value" id="low-risk-vuln">29</div>
                <div class="stat-label">低危漏洞</div>
                <div class="stat-trend trend-down">↘ -5</div>
            </div>
        </div>

        <!-- 内容网格 -->
        <div class="content-grid">
            <!-- 主要内容区 -->
            <div class="main-content">
                <!-- 网络拓扑图 -->
                <div class="topology-container">
                    <div class="section-title">网络拓扑监控</div>
                    <div class="topology-map" id="topology-map">
                        <!-- 网络节点 -->
                        <div class="network-node database" style="top: 20%; left: 15%;">
                            <span>数据库</span>
                        </div>
                        <div class="network-node server" style="top: 15%; left: 45%;">
                            <span>应用服务器</span>
                        </div>
                        <div class="network-node firewall" style="top: 25%; left: 75%;">
                            <span>防火墙</span>
                        </div>
                        <div class="network-node" style="top: 55%; left: 20%;">
                            <span>Web服务</span>
                        </div>
                        <div class="network-node database" style="top: 60%; left: 50%;">
                            <span>缓存服务</span>
                        </div>
                        <div class="network-node server" style="top: 50%; left: 80%;">
                            <span>负载均衡</span>
                        </div>

                        <!-- 连接线 -->
                        <div class="connection-line active" style="top: 22%; left: 23%; width: 150px; transform: rotate(5deg);"></div>
                        <div class="connection-line" style="top: 18%; left: 53%; width: 120px; transform: rotate(10deg);"></div>
                        <div class="connection-line active" style="top: 40%; left: 25%; width: 180px; transform: rotate(-15deg);"></div>
                    </div>
                </div>

                <!-- 趋势分析图 -->
                <div class="trend-container">
                    <div class="section-title">安全趋势分析</div>
                    <div class="trend-chart" id="trend-chart">
                        <div class="trend-line"></div>
                        <div class="trend-point" style="left: 10%; bottom: 30%;"></div>
                        <div class="trend-point" style="left: 25%; bottom: 45%;"></div>
                        <div class="trend-point" style="left: 40%; bottom: 35%;"></div>
                        <div class="trend-point" style="left: 55%; bottom: 60%;"></div>
                        <div class="trend-point" style="left: 70%; bottom: 40%;"></div>
                        <div class="trend-point" style="left: 85%; bottom: 25%;"></div>
                    </div>
                </div>
            </div>

            <!-- 中间内容区 -->
            <div class="middle-content">
                <!-- 30天数据分类条形图 -->
                <div class="chart-container">
                    <div class="section-title">漏洞等级分布</div>
                    <div class="chart" id="vuln-chart" style="height: 150px;">
                        <div class="chart-bar" style="height: 120px; background: #ff4d4f; flex: 0 0 40px;"></div>
                        <div class="chart-bar" style="height: 90px; background: #faad14; flex: 0 0 40px;"></div>
                        <div class="chart-bar" style="height: 70px; background: #1890ff; flex: 0 0 40px;"></div>
                        <div class="chart-bar" style="height: 50px; background: #52c41a; flex: 0 0 40px;"></div>
                        <div class="chart-bar" style="height: 40px; background: #722ed1; flex: 0 0 40px;"></div>
                        <div class="chart-bar" style="height: 30px; background: #13c2c2; flex: 0 0 40px;"></div>
                        <div class="chart-bar" style="height: 25px; background: #eb2f96; flex: 0 0 40px;"></div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #ff4d4f;"></div>
                            <span>严重</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #faad14;"></div>
                            <span>高危</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1890ff;"></div>
                            <span>中危</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #52c41a;"></div>
                            <span>低危</span>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="data-table">
                    <div class="section-title">最新扫描结果</div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>扫描时间</th>
                                <th>目标</th>
                                <th>发现漏洞</th>
                                <th>状态</th>
                                <th>风险等级</th>
                                <th>处理建议</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-08-27 14:30:12</td>
                                <td>192.168.1.100</td>
                                <td>SQL注入</td>
                                <td><span class="status-badge danger">未修复</span></td>
                                <td>高危</td>
                                <td>立即修复数据库权限</td>
                            </tr>
                            <tr>
                                <td>2024-08-27 14:25:08</td>
                                <td>web.example.com</td>
                                <td>XSS漏洞</td>
                                <td><span class="status-badge warning">处理中</span></td>
                                <td>中危</td>
                                <td>更新输入验证机制</td>
                            </tr>
                            <tr>
                                <td>2024-08-27 14:20:45</td>
                                <td>192.168.1.50</td>
                                <td>弱密码</td>
                                <td><span class="status-badge success">已修复</span></td>
                                <td>低危</td>
                                <td>已更新密码策略</td>
                            </tr>
                            <tr>
                                <td>2024-08-27 14:15:33</td>
                                <td>api.example.com</td>
                                <td>未授权访问</td>
                                <td><span class="status-badge danger">未修复</span></td>
                                <td>严重</td>
                                <td>紧急配置访问控制</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 3D地球 -->
                <div class="globe-container">
                    <div class="section-title">实时传输监控</div>
                    <div class="globe"></div>
                    <div class="traffic-display">8.7 TB</div>
                    <div class="traffic-label">今日传输流量</div>
                </div>

                <!-- 24小时热力图 -->
                <div class="heatmap-container">
                    <div class="section-title">24小时数据量热力</div>
                    <div class="heatmap" id="heatmap"></div>
                </div>

                <!-- 数据库分布饼图 -->
                <div class="pie-container">
                    <div class="section-title">数据库类型分布</div>
                    <div class="pie-chart"></div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1890ff;"></div>
                            <span>Oracle</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #52c41a;"></div>
                            <span>MySQL</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #faad14;"></div>
                            <span>MongoDB</span>
                        </div>
                    </div>
                </div>

                <!-- 风险雷达图 -->
                <div class="radar-container">
                    <div class="section-title">风险评估雷达</div>
                    <div class="radar-chart">
                        <svg viewBox="0 0 200 200">
                            <defs>
                                <radialGradient id="radarGrad">
                                    <stop offset="0%" style="stop-color:#1890ff;stop-opacity:0.3"/>
                                    <stop offset="100%" style="stop-color:#1890ff;stop-opacity:0"/>
                                </radialGradient>
                            </defs>
                            <!-- 雷达网格 -->
                            <circle cx="100" cy="100" r="80" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <circle cx="100" cy="100" r="60" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <circle cx="100" cy="100" r="40" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <circle cx="100" cy="100" r="20" fill="none" stroke="#d9d9d9" stroke-width="1"/>
                            <!-- 雷达线 -->
                            <line x1="100" y1="20" x2="100" y2="180" stroke="#d9d9d9" stroke-width="1"/>
                            <line x1="20" y1="100" x2="180" y2="100" stroke="#d9d9d9" stroke-width="1"/>
                            <line x1="42" y1="42" x2="158" y2="158" stroke="#d9d9d9" stroke-width="1"/>
                            <line x1="158" y1="42" x2="42" y2="158" stroke="#d9d9d9" stroke-width="1"/>
                            <!-- 数据多边形 -->
                            <polygon points="100,40 140,60 150,100 120,140 80,130" fill="url(#radarGrad)" stroke="#1890ff" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 实时时钟
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('clock').textContent = timeString;
        }

        // 生成热力图
        function generateHeatmap() {
            const heatmap = document.getElementById('heatmap');
            heatmap.innerHTML = ''; // 清空现有内容

            for (let i = 0; i < 24; i++) {
                const bar = document.createElement('div');
                bar.className = 'heat-bar';
                const intensity = Math.random() * 100 + 20;
                bar.style.setProperty('--intensity', intensity + '%');
                bar.title = `${i}:00 - ${intensity.toFixed(1)}GB`;
                heatmap.appendChild(bar);
            }
        }

        // 生成条形图
        function generateChart() {
            const chart = document.getElementById('chart');
            chart.innerHTML = ''; // 清空现有内容

            const categories = ['hr', 'crm', 'iot', 'log', 'other'];

            for (let i = 0; i < 30; i++) {
                const bar = document.createElement('div');
                bar.className = `chart-bar ${categories[i % 5]}`;
                const height = Math.random() * 150 + 30;
                bar.style.height = height + 'px';
                bar.title = `第${i+1}天`;
                chart.appendChild(bar);
            }
        }

        // 数据刷新
        function refreshData() {
            // 模拟数据更新
            const kpis = ['total-assets', 'risk-assets', 'compliance-rate', 'scan-count', 'vulnerability-count', 'high-risk-vuln', 'medium-risk-vuln', 'low-risk-vuln'];
            kpis.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    let currentValue = parseFloat(element.textContent.replace(/[%,万个]/g, '')) || 0;
                    const change = Math.random() * 10 - 5;
                    let newValue = Math.max(0, currentValue + change);

                    // 格式化数字显示
                    if (id === 'compliance-rate') {
                        newValue = Math.min(100, Math.max(0, newValue));
                        element.textContent = newValue.toFixed(1) + '%';
                    } else if (id === 'total-assets') {
                        element.textContent = newValue.toFixed(1);
                    } else if (id === 'vulnerability-count') {
                        element.textContent = newValue.toFixed(1);
                    } else {
                        element.textContent = Math.round(newValue);
                    }
                }
            });

            // 刷新图表
            generateHeatmap();
            updateNetworkStatus();
        }

        // 更新网络状态
        function updateNetworkStatus() {
            const nodes = document.querySelectorAll('.network-node');
            const lines = document.querySelectorAll('.connection-line');

            // 随机更新节点状态
            nodes.forEach(node => {
                const isActive = Math.random() > 0.3;
                if (isActive) {
                    node.style.opacity = '1';
                    node.style.transform = 'scale(1)';
                } else {
                    node.style.opacity = '0.6';
                    node.style.transform = 'scale(0.9)';
                }
            });

            // 随机更新连接线状态
            lines.forEach(line => {
                const isActive = Math.random() > 0.5;
                if (isActive) {
                    line.classList.add('active');
                } else {
                    line.classList.remove('active');
                }
            });
        }

        // 更新趋势图
        function updateTrendChart() {
            const points = document.querySelectorAll('.trend-point');
            points.forEach(point => {
                const newBottom = Math.random() * 60 + 20;
                point.style.bottom = newBottom + '%';
            });
        }

        // 添加新的扫描记录
        function addScanRecord() {
            const targets = ['192.168.1.101', '192.168.1.102', 'app.example.com', 'db.example.com'];
            const vulns = ['SQL注入', 'XSS漏洞', '弱密码', '未授权访问', '信息泄露', '缓冲区溢出'];
            const statuses = [
                { class: 'danger', text: '未修复' },
                { class: 'warning', text: '处理中' },
                { class: 'success', text: '已修复' }
            ];
            const risks = ['严重', '高危', '中危', '低危'];
            const suggestions = [
                '立即修复数据库权限',
                '更新输入验证机制',
                '已更新密码策略',
                '紧急配置访问控制',
                '升级系统补丁',
                '加强监控机制'
            ];

            const tbody = document.querySelector('.table tbody');
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');

            const randomTarget = targets[Math.floor(Math.random() * targets.length)];
            const randomVuln = vulns[Math.floor(Math.random() * vulns.length)];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            const randomRisk = risks[Math.floor(Math.random() * risks.length)];
            const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];

            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${timeString}</td>
                <td>${randomTarget}</td>
                <td>${randomVuln}</td>
                <td><span class="status-badge ${randomStatus.class}">${randomStatus.text}</span></td>
                <td>${randomRisk}</td>
                <td>${randomSuggestion}</td>
            `;

            tbody.insertBefore(newRow, tbody.firstChild);

            // 保持最多10条记录
            while (tbody.children.length > 10) {
                tbody.removeChild(tbody.lastChild);
            }
        }

        // 初始化
        window.addEventListener('load', () => {
            generateHeatmap();
            generateChart();
            updateClock();
            updateNetworkStatus();
            updateTrendChart();

            setInterval(updateClock, 1000);
            setInterval(refreshData, 30000); // 30秒刷新数据
            setInterval(updateNetworkStatus, 15000); // 15秒更新网络状态
            setInterval(updateTrendChart, 20000); // 20秒更新趋势图
            setInterval(addScanRecord, 60000); // 60秒添加新扫描记录
        });
    </script>
</body>
</html>
