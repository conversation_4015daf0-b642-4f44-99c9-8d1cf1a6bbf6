<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>济南市失陷主机治理专项行动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f0f2f5;
            color: #333;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 200px;
            height: 100vh;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px 16px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-bottom: 4px;
        }

        .sidebar-subtitle {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
        }

        .sidebar-menu {
            padding: 16px 0;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .menu-item.active {
            background: rgba(255,255,255,0.15);
            color: white;
            border-left-color: #60a5fa;
        }

        .menu-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.8;
        }

        /* 顶部导航栏 */
        .header {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 200px;
            width: calc(100% - 200px);
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            z-index: 999;
        }

        .page-title-header {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .date-range-picker {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .clock {
            font-size: 14px;
            color: #6b7280;
        }

        /* 主容器 */
        .main-container {
            margin-top: 60px;
            margin-left: 200px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            background: #f0f2f5;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.3s;
            position: relative;
            border-left: 4px solid #e5e7eb;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-card.red {
            border-left-color: #ef4444;
        }

        .stat-card.orange {
            border-left-color: #f59e0b;
        }

        .stat-card.green {
            border-left-color: #10b981;
        }

        .stat-card.blue {
            border-left-color: #3b82f6;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1;
        }

        .stat-label {
            color: #6b7280;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .stat-trend {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up {
            color: #10b981;
        }

        .trend-down {
            color: #ef4444;
        }

        .trend-neutral {
            color: #6b7280;
        }

        /* 内容区域 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .chart-container {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #3b82f6;
            border-radius: 2px;
        }

        /* 饼图容器 */
        .pie-chart {
            width: 200px;
            height: 200px;
            margin: 0 auto 16px;
            position: relative;
        }

        .pie-chart svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        /* 图例 */
        .chart-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #6b7280;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .header {
                left: 0;
                width: 100%;
            }
            
            .main-container {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">主要功能</div>
            <div class="sidebar-subtitle">数据跨境合规监管</div>
        </div>
        <div class="sidebar-menu">
            <a href="#" class="menu-item active">
                <span class="menu-icon">📊</span>
                协调调度
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">🔍</span>
                系统主机治理
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">📈</span>
                网络流量监控
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">⚠️</span>
                应急响应分析
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">📋</span>
                事件调查
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">🔒</span>
                取证分析
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">🛡️</span>
                威胁情报
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">📊</span>
                应急响应
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">⚙️</span>
                系统管理
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">📝</span>
                全程追踪
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">📊</span>
                上报情况
            </a>
            <a href="#" class="menu-item">
                <span class="menu-icon">⚠️</span>
                漏洞告警
            </a>
        </div>
    </div>

    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="page-title-header">济南市失陷主机治理专项行动</div>
        <div class="header-actions">
            <div class="date-range-picker">
                <span>本月</span>
                <span>本一个月</span>
                <span>2024-08-01</span>
                <span>至</span>
                <span>2024-08-31</span>
                <button class="btn-primary">查询</button>
            </div>
            <span class="clock" id="clock">最后更新时间: 2024-08-27 14:30:25</span>
        </div>
    </div>
